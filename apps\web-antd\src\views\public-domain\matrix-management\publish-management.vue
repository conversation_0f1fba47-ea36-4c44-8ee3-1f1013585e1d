<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { useI18n } from '@vben/locales';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  <PERSON><PERSON> as <PERSON><PERSON>on,
  Col as ACol,
  Dropdown as <PERSON>ropdown,
  <PERSON>loatButton as <PERSON><PERSON>loat<PERSON>utton,
  List as AList,
  ListItem as AListItem,
  ListItemMeta as AListItemMeta,
  Menu as AMenu,
  MenuItem as AMenuItem,
  Modal as AModal,
  Progress as AProgress,
  Row as ARow,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tag as ATag,
  message,
} from 'ant-design-vue';

import PublishModal from './modal/publish-modal.vue';

const { t } = useI18n();

// 发布模态框控制
const publishModalVisible = ref(false);

const showPublishModal = (platformType?: number) => {
  publishModalVisible.value = true;
  // 传递平台类型给modal
  if (platformType) {
    publishPlatformType.value = platformType;
  }
};

// 类型定义
// interface CountObj {
//   account_count: number;
//   douyin_account_count: number;
//   kuaishou_account_count: number;
//   shipinghao_account_count: number;
//   xiaohong_account_count: number;
//   exposure_count_total: number;
//   like_count_total: number;
//   comment_total: number;
// }

interface TaskItem {
  id: number;
  name: string;
  channle_type: number;
  create_time: string;
  percentage: number;
  published_task: number;
  published_wait_task: number;
  published_fail_task: number;
  status: number; // 1: 进行中, 2: 已终止, 3: 已完成
}

interface TabItem {
  id: string;
  name: string;
}

// 状态管理
// const uid = ref('test'); // TODO: 替换为实际用户ID
const tabId = ref('');
// const countObj = ref<CountObj>({
//   account_count: 0,
//   douyin_account_count: 0,
//   kuaishou_account_count: 0,
//   shipinghao_account_count: 0,
//   xiaohong_account_count: 0,
//   exposure_count_total: 0,
//   like_count_total: 0,
//   comment_total: 0,
// });
const taskList = ref<TaskItem[]>([]);
// const router = useRouter();
const confirmModalVisible = ref(false);
const confirmModalTitle = ref('');
const confirmModalContent = ref('');
const currentTaskId = ref(0);
const actionType = ref(''); // 'delete' | 'terminate'
const publishPlatformType = ref(1);

// 标签数据
const tabs: TabItem[] = [
  { id: '1', name: t('matrix-management.homepage.platforms.douyin') },
  { id: '2', name: t('matrix-management.homepage.platforms.kuaishou') },
  { id: '3', name: t('matrix-management.homepage.platforms.shipinghao') },
  { id: '4', name: t('matrix-management.homepage.platforms.xiaohong') },
];

// 生命周期
onMounted(() => {
  // checkLoginStatus();
  loadTaskList();
});

const loadTaskList = () => {
  // 模拟数据 TODO: 替换为实际API调用
  taskList.value = [
    {
      id: 1,
      name: '任务1',
      channle_type: 1,
      create_time: '2023-10-01 10:00',
      percentage: 50,
      published_task: 2,
      published_wait_task: 1,
      published_fail_task: 1,
      status: 1,
    },
    {
      id: 2,
      name: '任务2',
      channle_type: 2,
      create_time: '2023-10-02 11:00',
      percentage: 100,
      published_task: 5,
      published_wait_task: 0,
      published_fail_task: 0,
      status: 3,
    },
    {
      id: 3,
      name: '任务3',
      channle_type: 3,
      create_time: '2023-10-03 12:00',
      percentage: 75,
      published_task: 3,
      published_wait_task: 1,
      published_fail_task: 1,
      status: 2,
    },
    {
      id: 4,
      name: '任务4',
      channle_type: 4,
      create_time: '2023-10-04 13:00',
      percentage: 25,
      published_task: 1,
      published_wait_task: 2,
      published_fail_task: 1,
      status: 1,
    },
  ];
};

const handleTabChange = (key: string) => {
  tabId.value = key;
  loadTaskList();
};

const getPlatformName = (type: number): string => {
  const platformMap: Record<number, string> = {
    1: 'D音',
    2: 'K手',
    3: '视频号',
    4: '小红薯',
  };
  return platformMap[type] || '未知平台';
};

const getPlatformColor = (type: number): string => {
  const colorMap: Record<number, string> = {
    1: '#166DFD',
    2: '#FF7B00',
    3: '#FFDC00',
    4: '#FF0000',
  };
  return colorMap[type] || '#ccc';
};

const getProgressStatus = (
  percent: number,
): 'active' | 'exception' | 'success' => {
  if (percent === 100) return 'success';
  return 'active';
};

const handleViewTask = (taskId: number, channelType: number) => {
  console.warn(`查看任务: ${taskId}, 平台: ${channelType}`);
  // TODO
  // router.push({
  //   path: '/public-domain',
  //   query: { taskId, type: 2, selLabel: channelType }
  // });
};

const handleDeleteTask = (taskId: number) => {
  currentTaskId.value = taskId;
  actionType.value = 'delete';
  confirmModalTitle.value = '提示';
  confirmModalContent.value = '是否确认删除该任务?';
  confirmModalVisible.value = true;
};

const handleTerminateTask = (taskId: number) => {
  currentTaskId.value = taskId;
  actionType.value = 'terminate';
  confirmModalTitle.value = '提示';
  confirmModalContent.value = '任务一旦终止后不可启用,是否确认?';
  confirmModalVisible.value = true;
};

const handleConfirmOk = () => {
  message.success('操作成功');
  loadTaskList(); // 重新加载任务列表
  confirmModalVisible.value = false;
};

const handleConfirmCancel = () => {
  confirmModalVisible.value = false;
};
</script>

<template>
  <Page>
    <!-- <a-layout class="layout"> -->
    <!-- 标签页切换 -->
    <ATabs v-model:active-key="tabId" class="mt-4" @change="handleTabChange">
      <ATabPane v-for="tab in tabs" :key="tab.id" :tab="tab.name" />
    </ATabs>

    <!-- 任务列表 -->
    <AList
      class="task-list mt-4"
      :data-source="taskList"
      :bordered="false"
      item-layout="horizontal"
    >
      <template #renderItem="{ item }">
        <AListItem
          :style="{
            borderRadius: '8px',
            marginBottom: '16px',
            padding: '16px',
          }"
        >
          <AListItemMeta>
            <template #title>
              <div class="task-title-meta-time">
                <div class="task-title-meta">
                  <div class="task-title">{{ item.name }}</div>
                  <ATag
                    :color="getPlatformColor(item.channle_type)"
                    class="task-meta"
                  >
                    {{ getPlatformName(item.channle_type) }}
                  </ATag>
                </div>
                <span class="task-time">{{ item.create_time }}</span>
              </div>
            </template>
          </AListItemMeta>

          <div class="task-content">
            <AProgress
              :percent="item.percentage"
              :status="getProgressStatus(item.percentage)"
              :style="{ width: '100%', marginBottom: '16px' }"
            />

            <ARow class="task-stats" :gutter="8">
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.success') }}
                </div>
              </ACol>
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_wait_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.waiting') }}
                </div>
              </ACol>
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_fail_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.failed') }}
                </div>
              </ACol>
            </ARow>

            <div class="task-actions">
              <AButton
                v-if="[1, 2, 4].includes(item.channle_type)"
                type="link"
                @click="handleViewTask(item.id, item.channle_type)"
              >
                {{ t('matrix-management.homepage.actions.viewTask') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 1 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                @click="handleTerminateTask(item.id)"
              >
                {{ t('matrix-management.homepage.actions.terminateTask') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 3 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                disabled
              >
                {{ t('matrix-management.homepage.taskStatus.completed') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 2 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                disabled
              >
                {{ t('matrix-management.homepage.taskStatus.terminated') }}
              </AButton>

              <AButton
                :disabled="!(item.status === 2 || item.status === 3)"
                type="link"
                @click="handleDeleteTask(item.id)"
              >
                {{ t('matrix-management.homepage.actions.deleteTask') }}
              </AButton>
            </div>
          </div>
        </AListItem>
      </template>
    </AList>

    <!-- 确认对话框 -->
    <AModal
      v-model:visible="confirmModalVisible"
      :title="confirmModalTitle"
      @ok="handleConfirmOk"
      @cancel="handleConfirmCancel"
    >
      <template #default>
        <p>{{ confirmModalContent }}</p>
      </template>
      <template #footer>
        <AButton key="back" @click="handleConfirmCancel">
          {{ t('matrix-management.common.cancel') }}
        </AButton>
        <AButton key="submit" type="primary" @click="handleConfirmOk">
          {{ t('matrix-management.common.confirm') }}
        </AButton>
      </template>
    </AModal>

    <!-- 发布按钮 -->
    <ADropdown
      placement="topLeft"
      :arrow="{ pointAtCenter: true }"
      :style="{ right: '24px', bottom: '24px', position: 'fixed' }"
    >
      <AFloatButton type="primary" shape="circle">
        <template #icon>
          <PlusOutlined />
        </template>
      </AFloatButton>
      <template #overlay>
        <AMenu>
          <AMenuItem @click="showPublishModal(1)">
            <span>D音</span>
          </AMenuItem>
          <AMenuItem @click="showPublishModal(2)">
            <span>K手</span>
          </AMenuItem>
          <AMenuItem @click="showPublishModal(3)">
            <span>视频号</span>
          </AMenuItem>
          <AMenuItem @click="showPublishModal(4)">
            <span>小红薯</span>
          </AMenuItem>
        </AMenu>
      </template>
    </ADropdown>

    <!-- 发布模态框 -->
    <PublishModal
      v-model:visible="publishModalVisible"
      :type="publishPlatformType"
    />
  </Page>
</template>

<style scoped lang="scss">
.stat-card {
  margin-bottom: 20px;

  .stat-label {
    margin-bottom: 4px;
    font-size: 14px;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
  }

  .platform-stat {
    padding: 10px 0;
    text-align: left;
  }
}

.task-list {
  .task-title-meta-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    .task-title-meta {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-left: auto;
    }

    .task-title {
      font-size: 16px;
      font-weight: 500;
    }

    .task-meta {
      font-size: 12px;
    }

    .task-time {
      font-size: 12px;
    }
  }

  .task-content {
    width: 100%;
  }

  .task-stats {
    margin-top: 16px; // 添加顶部间距以便与进度条分离
    text-align: center;

    .stat-item {
      .stat-number {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: 500;
      }

      .stat-desc {
        font-size: 12px;
      }
    }
  }

  .task-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px; // 添加顶部间距以便与统计数据分离

    .ant-btn-link {
      margin-left: 10px;
    }
  }
}

.ant-progress {
  margin-bottom: 16px; // 确保进度条与统计数据之间有足够的间距
}

.back-button {
  position: fixed;
  right: 20px;
  bottom: 80px;
  z-index: 1000; // 确保返回按钮在最上层
  width: 48px;
  height: 48px;
}

.ant-tabs-bar {
  margin-bottom: 20px;
}

.ant-list-item {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

.ant-tag {
  margin-right: 8px;
}

/* 任务标题和元信息布局优化 */
.task-title-meta-time {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.task-title {
  max-width: 50%;
  // overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
}

.task-meta {
  margin-left: 8px;
}

.task-time {
  margin-left: auto;
  font-size: 12px;
  white-space: nowrap;
}
</style>
