<script setup lang="ts">
import type { TaskOperationParams, VideoTaskItem } from '#/api/core';

import { ref } from 'vue';

import { Button as AButton, Modal as AModal, message } from 'ant-design-vue';

import { deleteTask, endTask } from '#/api/core';

import TaskDetailModal from './TaskDetailModal.vue';

// Props
interface Props {
  task: VideoTaskItem;
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

// Emits
interface Emits {
  (e: 'action', action: string, task: VideoTaskItem): void;
}

// 平台映射
const platformMap: Record<
  number,
  { color: string; icon: string; name: string }
> = {
  1: { name: '抖音', color: '#ff4757', icon: '🎵' },
  2: { name: '快手', color: '#ff6b35', icon: '⚡' },
  3: { name: '视频号', color: '#26de81', icon: '📱' },
  4: { name: '小红书', color: '#fd79a8', icon: '📖' },
};

// 弹窗状态
const modalVisible = ref(false);

// 确认弹窗状态
const confirmModalVisible = ref(false);
const confirmAction = ref('');
const confirmTitle = ref('');
const confirmContent = ref('');
const operationLoading = ref(false);

// 终止任务
const handleEndTask = async () => {
  try {
    operationLoading.value = true;
    const params: TaskOperationParams = {
      uid: 7,
      id: props.task.id,
    };

    await endTask(params);
    message.success('任务终止成功！');

    // 通知父组件刷新列表
    emit('action', 'refresh', props.task);
  } catch (error) {
    console.error('终止任务失败:', error);
    message.error('终止任务失败，请重试');
  } finally {
    operationLoading.value = false;
    confirmModalVisible.value = false;
  }
};

// 删除任务
const handleDeleteTask = async () => {
  try {
    operationLoading.value = true;
    const params: TaskOperationParams = {
      uid: 7,
      id: props.task.id,
    };

    await deleteTask(params);
    message.success('任务删除成功！');

    // 通知父组件刷新列表
    emit('action', 'refresh', props.task);
  } catch (error) {
    console.error('删除任务失败:', error);
    message.error('删除任务失败，请重试');
  } finally {
    operationLoading.value = false;
    confirmModalVisible.value = false;
  }
};

// 确认操作
const handleConfirm = () => {
  if (confirmAction.value === 'stop') {
    handleEndTask();
  } else if (confirmAction.value === 'delete') {
    handleDeleteTask();
  }
};

// 处理操作
const handleAction = (action: string) => {
  switch (action) {
    case 'delete': {
      // 显示删除任务确认弹窗
      confirmAction.value = 'delete';
      confirmTitle.value = '删除任务';
      confirmContent.value = '是否确认删除该任务？';
      confirmModalVisible.value = true;

      break;
    }
    case 'stop': {
      // 显示终止任务确认弹窗
      confirmAction.value = 'stop';
      confirmTitle.value = '终止任务';
      confirmContent.value = '任务一旦终止后不可启用，是否确认？';
      confirmModalVisible.value = true;

      break;
    }
    case 'view': {
      // 打开任务详情弹窗
      modalVisible.value = true;

      break;
    }
    default: {
      // 其他操作传递给父组件
      emit('action', action, props.task);
    }
  }
};

// 处理弹窗内的操作
const handleModalAction = (action: string, item: any) => {
  // 传递给父组件
  emit('action', action, item);
};
</script>

<template>
  <div class="task-card">
    <!-- 任务卡片头部 -->
    <div class="task-header">
      <div class="task-platform">
        <span class="platform-icon">{{
          platformMap[task.channle_type]?.icon || '📱'
        }}</span>
        <span class="platform-name">{{
          platformMap[task.channle_type]?.name || '未知平台'
        }}</span>
      </div>
      <div class="task-time">{{ task.create_time }}</div>
    </div>

    <!-- 任务名称 -->
    <div class="task-name">{{ task.name }}</div>

    <!-- 进度条 -->
    <div class="task-progress">
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${task.percentage}%` }"
        ></div>
      </div>
      <span class="progress-text">{{ task.percentage }}%</span>
    </div>

    <!-- 任务统计 -->
    <div class="task-stats">
      <div class="stat-item success">
        <div class="stat-number">{{ task.published_task }}</div>
        <div class="stat-label">发布成功</div>
      </div>
      <div class="stat-item waiting">
        <div class="stat-number">{{ task.published_wait_task }}</div>
        <div class="stat-label">待发布</div>
      </div>
      <div class="stat-item failed">
        <div class="stat-number">{{ task.published_fail_task }}</div>
        <div class="stat-label">发布失败</div>
      </div>
    </div>

    <!-- 任务操作按钮 -->
    <div class="task-actions">
      <template v-if="[1, 2, 4].includes(task.channle_type)">
        <!-- status=1时显示终止任务按钮 -->
        <!-- 查看任务按钮 - 始终显示 -->
        <AButton size="small" type="primary" @click="handleAction('view')">
          查看任务
        </AButton>
        <AButton
          v-if="task.status === 1"
          size="small"
          @click="handleAction('stop')"
        >
          终止任务
        </AButton>

        <!-- status=3时显示已完成按钮 -->
        <AButton
          v-else-if="task.status === 3"
          size="small"
          type="default"
          @click="handleAction('complete')"
        >
          已完成
        </AButton>

        <!-- status=2时显示已终止按钮 -->
        <AButton
          v-else-if="task.status === 2"
          size="small"
          danger
          @click="handleAction('terminate')"
        >
          已终止
        </AButton>
      </template>

      <!-- status=2或3时显示删除任务按钮 -->
      <AButton
        v-if="task.status === 2 || task.status === 3"
        size="small"
        danger
        @click="handleAction('delete')"
      >
        删除任务
      </AButton>
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetailModal
      v-model:visible="modalVisible"
      :type="2"
      :channel-id="task.channle_type"
      :task-id="task.id.toString()"
      @action="handleModalAction"
    />

    <!-- 确认操作弹窗 -->
    <AModal
      v-model:visible="confirmModalVisible"
      :title="confirmTitle"
      :confirm-loading="operationLoading"
      @ok="handleConfirm"
      @cancel="confirmModalVisible = false"
    >
      <p>{{ confirmContent }}</p>
    </AModal>
  </div>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .task-actions {
    flex-direction: column;
  }

  .task-actions .ant-btn {
    flex: none;
    width: 100%;
  }
}

.task-card {
  padding: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgb(0 0 0 / 12%);
    transform: translateY(-2px);
  }
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.task-platform {
  display: flex;
  gap: 8px;
  align-items: center;
}

.platform-icon {
  font-size: 16px;
}

.platform-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.task-time {
  font-size: 12px;
  color: #999;
}

.task-name {
  margin-bottom: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  white-space: nowrap;
}

.task-progress {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  margin-bottom: 8px;
  overflow: hidden;
  background-color: #f0f0f0;
  border-radius: 3px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #36cfc9 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #1890ff;
}

.task-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-item.success .stat-number {
  color: #52c41a;
}

.stat-item.waiting .stat-number {
  color: #1890ff;
}

.stat-item.failed .stat-number {
  color: #ff4d4f;
}

.task-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.task-actions .ant-btn {
  flex: 1;
  min-width: 0;
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
}
</style>
