import { requestClient } from '#/api/request';

/**
 * 用户信息数据接口
 */
export interface UserInfoData {
  ai_copywriting_times: number;
  ai_title_times: number;
  avatar: string;
  avatar_count: number;
  avatar_twin_count: number;
  balance: string;
  brokerage: string;
  create_time: string;
  delete_time: null | string;
  distribution_level: number;
  distribution_level_id: number;
  distribution_level_logic: number;
  high_fidelity_words_number: number;
  id: number;
  is_execute_number: number;
  is_freeze: number;
  is_member: number;
  is_new: number;
  is_shareholder: number;
  maturity_time: null | string;
  nickname: string;
  openid: string;
  partner_freeze: number;
  partner_id: number;
  partner_name: string;
  partner_refuse: string;
  partner_status: number;
  partner_telphone: string;
  pid: string;
  qrcode: string;
  second: number;
  second_infinite: number;
  telphone: string;
  update_time: string;
  voice_twin_count: number;
  xunfei_sound_clone_words_number: number;
  xunfei_sound_generate_words_number: number;
}

/**
 * 获取用户信息
 * @returns Promise<UserInfoData>
 */
export async function getUserInfoApi(): Promise<UserInfoData> {
  return requestClient.post<UserInfoData>('/mobile/user/userInfo');
}

// 重新导出getCloneSetConfig接口供个人信息页面使用
export { getCloneSetConfig } from './digital-human';
