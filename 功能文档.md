# AIWorld_web 项目功能文档

## 项目概述

AIWorld_web 是一个基于 Vue Vben Admin 框架构建的AI工具集成平台，提供多种AI相关的业务功能和服务。项目采用现代化的前端技术栈，支持企业级应用开发需求。

### 基本信息
- **项目名称**: AIWorld_web (基于 vben-admin-monorepo)
- **版本**: 5.5.7
- **许可证**: MIT
- **架构模式**: Monorepo (使用 pnpm workspace)
- **主要技术栈**: Vue 3 + Vite + TypeScript + Ant Design Vue

## 技术架构

### 核心技术栈
- **前端框架**: Vue 3.x
- **构建工具**: Vite
- **开发语言**: TypeScript
- **UI组件库**: Ant Design Vue 4.x + Ant Design X Vue
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **包管理器**: pnpm (>= 9.12.0)
- **Node版本**: >= 20.10.0

### 开发工具链
- **代码规范**: ESLint + Prettier + Stylelint
- **类型检查**: TypeScript + vue-tsc
- **测试框架**: Vitest + Playwright
- **构建优化**: Turbo (monorepo 构建工具)
- **Git钩子**: Lefthook
- **提交规范**: Commitizen + Commitlint

## 项目结构

### 根目录结构
```
AIWorld_web/
├── apps/                    # 应用程序目录
│   ├── backend-mock/        # 后端模拟服务
│   └── web-antd/           # 主前端应用 (Ant Design Vue版本)
├── packages/               # 共享包目录
│   ├── @core/              # 核心功能包
│   ├── constants/          # 常量定义
│   ├── effects/            # 副作用处理
│   ├── icons/              # 图标库
│   ├── locales/            # 国际化
│   ├── preferences/        # 偏好设置
│   ├── stores/             # 状态管理
│   ├── styles/             # 样式库
│   ├── types/              # 类型定义
│   └── utils/              # 工具函数
├── internal/               # 内部配置包
│   ├── lint-configs/       # 代码规范配置
│   ├── node-utils/         # Node.js工具
│   ├── tailwind-config/    # Tailwind CSS配置
│   ├── tsconfig/           # TypeScript配置
│   └── vite-config/        # Vite配置
└── scripts/                # 构建和部署脚本
```

### 主应用结构 (apps/web-antd)
```
web-antd/
├── src/
│   ├── adapter/            # 适配器层
│   ├── api/                # API接口定义
│   ├── components/         # 公共组件
│   ├── layouts/            # 布局组件
│   ├── locales/            # 国际化文件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   └── views/              # 页面视图
├── public/                 # 静态资源
└── dist/                   # 构建输出
```

## 业务功能模块

### 1. AI数字人 (ai-digital-human)
**路径**: `apps/web-antd/src/views/ai-digital-human/`

**主要功能**:
- **形象克隆**: 支持数字人形象的AI克隆和训练
- **声音克隆**: 提供AI声音克隆和语音合成功能
- **视频创作**: AI驱动的数字人视频生成和编辑
- **素材管理**: 数字人相关素材的上传、管理和使用

**核心文件**:
- `index.vue` - 主界面，包含形象克隆、声音克隆、视频创作入口
- `ai-video-creation.vue` - AI视频创作功能
- `ai-voice-clone.vue` - AI声音克隆功能
- `public-voice.json` - 公共语音库配置

### 2. AI热点追踪 (ai-hotspot-tracking)
**路径**: `apps/web-antd/src/views/ai-hotspot-tracking/`

**主要功能**:
- **热点内容分析**: 实时追踪和分析网络热点
- **文案生成**: 基于热点内容自动生成营销文案
- **一键仿写**: 智能文案改写和优化功能

### 3. AI视频混剪 (ai-video-mixing)
**路径**: `apps/web-antd/src/views/ai-video-mixing/`

**主要功能**:
- **智能视频剪辑**: AI驱动的视频自动剪辑
- **素材混合**: 多素材智能组合和混剪
- **效果优化**: 视频质量和效果的AI优化

### 4. 企业立项 (enterprise-project)
**路径**: `apps/web-antd/src/views/enterprise-project/`

**主要功能**:
- **项目立项管理**: 企业项目的创建和管理
- **AI辅助分析**: 项目可行性的AI分析
- **报告生成**: 自动生成项目分析报告

### 5. 定位诊断 (positioning)
**路径**: `apps/web-antd/src/views/positioning/`

**主要功能**:
- **AI诊断** (`ai-diagnosis.vue`): 智能业务诊断分析
- **商业定位** (`ai-business-positioning.vue`): AI辅助商业定位
- **账号包装** (`ai-account-packaging.vue`): 社交账号的AI包装策略

### 6. 公域管理 (public-domain)
**路径**: `apps/web-antd/src/views/public-domain/matrix-management/`

**主要功能**:
- **矩阵管理** (`matrix.vue`): 公域流量矩阵管理
- **账号管理** (`account.vue`): 多账号统一管理
- **发布管理** (`publish-management.vue`): 内容发布和调度
- **群组管理** (`group-management.vue`): 用户群组管理
- **数据看板** (`dashboard.vue`): 运营数据分析

### 7. 聊天空间 (chatspace)
**路径**: `apps/web-antd/src/views/chatspace/bot-0/`

**主要功能**:
- **AI对话**: 智能聊天机器人交互
- **多场景支持**: 支持不同类型的AI助手 (如AI设计师)
- **上下文管理**: 对话历史和上下文保持

### 8. 用户中心 (user)
**路径**: `apps/web-antd/src/views/user/`

**主要功能**:
- **个人资料** (`profile.vue`): 用户信息管理
- **偏好设置**: 个性化配置管理

### 9. 仪表板 (dashboard)
**路径**: `apps/web-antd/src/views/dashboard/`

**主要功能**:
- **分析看板** (`analytics/`): 数据分析和可视化
- **工作空间** (`workspace/`): 个人工作台

## 核心包说明

### @core 包
**路径**: `packages/@core/`
- **base**: 基础组件和功能
- **composables**: Vue 3 组合式API
- **preferences**: 偏好设置管理
- **ui-kit**: UI组件库

### effects 包
**路径**: `packages/effects/`
- **access**: 权限控制
- **common-ui**: 通用UI组件
- **hooks**: 自定义钩子
- **layouts**: 布局组件
- **plugins**: 插件系统
- **request**: 请求处理

### 其他核心包
- **constants**: 全局常量定义
- **icons**: 图标库管理
- **locales**: 国际化支持
- **stores**: 状态管理
- **styles**: 样式系统
- **types**: TypeScript类型定义
- **utils**: 工具函数库

## 配置文件说明

### 构建配置
- `vite.config.mts` - Vite构建配置
- `turbo.json` - Turbo构建配置
- `tsconfig.json` - TypeScript配置

### 代码规范
- `eslint.config.mjs` - ESLint配置
- `stylelint.config.mjs` - Stylelint配置
- `lefthook.yml` - Git钩子配置

### 包管理
- `package.json` - 项目依赖和脚本
- `pnpm-workspace.yaml` - pnpm工作空间配置
- `pnpm-lock.yaml` - 依赖锁定文件

## 开发指南

### 环境要求
- Node.js >= 20.10.0
- pnpm >= 9.12.0

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
# 启动主应用
pnpm dev:antd

# 启动所有应用
pnpm dev
```

### 构建部署
```bash
# 构建主应用
pnpm build:antd

# 构建所有应用
pnpm build
```

### 代码检查
```bash
# 代码格式化
pnpm format

# 代码检查
pnpm lint

# 类型检查
pnpm check:type
```

## 特性亮点

1. **现代化技术栈**: 采用Vue 3、Vite、TypeScript等最新技术
2. **企业级架构**: Monorepo架构，模块化设计，易于维护和扩展
3. **AI功能集成**: 集成多种AI工具和服务，提供完整的AI解决方案
4. **国际化支持**: 内置完善的国际化方案
5. **主题定制**: 支持多套主题色彩和自定义主题
6. **权限管理**: 内置完善的动态路由权限生成方案
7. **响应式设计**: 支持多设备适配
8. **开发体验**: 完整的开发工具链，提供良好的开发体验

---

*本文档基于项目当前状态生成，如有更新请及时同步修改。*
