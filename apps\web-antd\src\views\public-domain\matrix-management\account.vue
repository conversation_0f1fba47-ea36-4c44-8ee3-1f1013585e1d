<script setup lang="ts">
import type { AccountGroupItem, GroupManagementListParams } from '#/api/core';
import type { AIAccountApi } from '#/api/core/ai-account';

import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Avatar as AAvatar,
  Badge as ABadge,
  But<PERSON> as AButton,
  Card as ACard,
  Col as ACol,
  Modal as AModal,
  Pagination as APagination,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Spin as ASpin,
  TabPane as ATabPane,
  Tabs as ATabs,
  message,
} from 'ant-design-vue';

import { getGroupManagementList } from '#/api/core';
import {
  accountExchangeGroupApi,
  delAccountApi,
  getAccountListApi,
} from '#/api/core/ai-account';

import TaskDetailModal from './components/TaskDetailModal.vue';

// 初始化路由
const router = useRouter();

// 平台选项配置
const platformOptions = [
  { key: 1, label: '抖音', icon: '🎵' },
  { key: 2, label: '快手', icon: '⚡' },
  { key: 3, label: '视频号', icon: '📹' },
  { key: 4, label: '小红书', icon: '📖' },
];

// 响应式状态
const loading = ref(false);
const activeTab = ref(1); // 当前选中的平台
const accountList = ref<AIAccountApi.AccountItem[]>([]);

// 分页配置
const pagination = ref({
  current: 1, // 当前页码
  pageSize: 12, // 每页数量
  total: 0, // 总数量
  showSizeChanger: false, // 不显示页面大小选择器
  showQuickJumper: true, // 显示快速跳转
  showTotal: (total: number, range: [number, number]) =>
    `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
});

// 换组弹窗相关状态
const changeGroupModalVisible = ref(false);
const changeGroupLoading = ref(false);
const currentChangeAccount = ref<AIAccountApi.AccountItem | null>(null);
const availableGroups = ref<AccountGroupItem[]>([]);
const selectedGroupId = ref<number | undefined>(undefined);

// 作品弹窗相关状态
const worksModalVisible = ref(false);
const currentWorksAccount = ref<AIAccountApi.AccountItem | null>(null);

// 计算当前平台名称
const currentPlatformName = computed(() => {
  const platform = platformOptions.find((p) => p.key === activeTab.value);
  return platform?.label || '';
});

// 时间处理工具函数
const msToDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return {
    hasTime: `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`,
    date: `${year}-${month}-${day}`,
  };
};

const formatDate = (dateStr: string) => {
  return dateStr.replace(/\s+\d{2}:\d{2}:\d{2}$/, '');
};

// 授权状态判断函数
const isEmpower = (expiresIn: string): boolean => {
  try {
    const currentTime = new Date(
      msToDate(new Date()).hasTime.replaceAll('-', '/'),
    );
    const expireTime = new Date(formatDate(expiresIn).replaceAll('-', '/'));
    return currentTime < expireTime;
  } catch (error) {
    console.error('时间比较出错:', error);
    return false;
  }
};

// 加载账号列表数据
const loadAccountList = async () => {
  try {
    loading.value = true;

    const params: AIAccountApi.AccountListParams = {
      page: pagination.value.current,
      psize: pagination.value.pageSize,
      type: activeTab.value.toString(),
    };

    const response = await getAccountListApi(params);

    accountList.value = response.list;
    pagination.value.total = response.total;
    pagination.value.current = response.pindex;
  } catch (error) {
    console.error('加载账号列表失败:', error);
    message.error('加载账号列表失败，请稍后重试');
    accountList.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 处理平台切换
const handleTabChange = (key: number | string) => {
  activeTab.value = typeof key === 'string' ? Number(key) : key;
  pagination.value.current = 1; // 重置到第一页
};

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.value.current = page;
  pagination.value.pageSize = pageSize;
  loadAccountList();
};

// 监听平台切换，重新加载数据
watch(activeTab, () => {
  loadAccountList();
});

// 加载可选分组列表
const loadAvailableGroups = async () => {
  try {
    const params: GroupManagementListParams = {
      page: 1,
      psize: 2000, // 设置为2000获取所有分组
      type: activeTab.value,
    };

    const response = await getGroupManagementList(params);
    availableGroups.value = response.list;
  } catch (error) {
    console.error('加载分组列表失败:', error);
    message.error('加载分组列表失败，请稍后重试');
    availableGroups.value = [];
  }
};

// 查看作品
const handleViewWorks = (account: AIAccountApi.AccountItem) => {
  currentWorksAccount.value = account;
  worksModalVisible.value = true;
};

// 换组操作
const handleChangeGroup = async (account: AIAccountApi.AccountItem) => {
  currentChangeAccount.value = account;
  selectedGroupId.value = undefined;

  // 加载可选分组列表
  await loadAvailableGroups();

  changeGroupModalVisible.value = true;
};

// 换组弹窗取消
const handleChangeGroupModalCancel = () => {
  changeGroupModalVisible.value = false;
  currentChangeAccount.value = null;
  selectedGroupId.value = undefined;
};

// 换组提交
const handleChangeGroupSubmit = async () => {
  if (!currentChangeAccount.value || !selectedGroupId.value) {
    message.error('请选择目标分组');
    return;
  }

  try {
    changeGroupLoading.value = true;

    const params: AIAccountApi.AccountExchangeGroupParams = {
      id: [currentChangeAccount.value.id],
      account_group_id: selectedGroupId.value,
    };

    await accountExchangeGroupApi(params);
    message.success('换组成功');

    // 关闭弹窗并重新加载列表
    handleChangeGroupModalCancel();
    await loadAccountList();
  } catch (error) {
    console.error('换组失败:', error);
    message.error('换组失败，请稍后重试');
  } finally {
    changeGroupLoading.value = false;
  }
};

// 删除账号
const handleDelete = (account: AIAccountApi.AccountItem) => {
  AModal.confirm({
    title: '确认删除',
    content: `确定要删除账号 "${account.account_name}" 吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk: async () => {
      try {
        const params: AIAccountApi.DelAccountParams = {
          id: [account.id],
        };

        await delAccountApi(params);
        message.success('删除成功');

        // 重新加载列表
        await loadAccountList();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败，请稍后重试');
      }
    },
  });
};

// 返回到矩阵管理首页
const handleGoBack = () => {
  router.push('/public-domain/matrix-management-dashboard');
};

// 页面初始化
onMounted(() => {
  loadAccountList();
});
</script>

<template>
  <Page auto-content-height>
    <div class="account-management-container">
      <!-- 返回按钮 -->
      <div class="page-header">
        <AButton @click="handleGoBack" class="back-btn"> ← 返回 </AButton>
      </div>

      <!-- 平台选择标签页 -->
      <div class="tabs-header">
        <ATabs
          v-model:active-key="activeTab"
          @change="handleTabChange"
          class="platform-tabs"
        >
          <ATabPane
            v-for="platform in platformOptions"
            :key="platform.key"
            :tab="`${platform.icon} ${platform.label}`"
          />
        </ATabs>
      </div>

      <!-- 账号列表内容区域 -->
      <div class="account-content">
        <ASpin :spinning="loading" tip="加载中...">
          <!-- 账号卡片列表 -->
          <div v-if="accountList.length > 0" class="account-cards-container">
            <ARow :gutter="[16, 16]">
              <ACol
                v-for="account in accountList"
                :key="account.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
              >
                <ACard class="account-card" hoverable>
                  <!-- 授权状态标识 - 右上角 -->
                  <div class="auth-status-badge">
                    <ABadge
                      :status="
                        isEmpower(account.expires_in) ? 'success' : 'error'
                      "
                      :text="
                        isEmpower(account.expires_in) ? '已授权' : '已失效'
                      "
                    />
                  </div>

                  <!-- 账号头像 -->
                  <div class="account-avatar-section">
                    <AAvatar
                      :src="account.avatar"
                      :size="64"
                      class="account-avatar"
                    >
                      {{ account.account_name?.charAt(0) || '?' }}
                    </AAvatar>
                  </div>

                  <!-- 账号信息 -->
                  <div class="account-info-section">
                    <!-- 用户名 -->
                    <div class="account-name" :title="account.account_name">
                      {{ account.account_name }}
                    </div>

                    <!-- 所在分组 -->
                    <div
                      class="account-group"
                      :title="account.account_group_name"
                    >
                      分组：{{ account.account_group_name }}
                    </div>

                    <!-- 到期时间 -->
                    <div class="account-expire-time">
                      到期：{{ formatDate(account.expires_in) }}
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="account-actions">
                    <AButton
                      v-if="activeTab !== 3"
                      type="primary"
                      size="small"
                      @click="handleViewWorks(account)"
                    >
                      作品
                    </AButton>
                    <AButton
                      type="default"
                      size="small"
                      @click="handleChangeGroup(account)"
                    >
                      换组
                    </AButton>
                    <AButton
                      type="primary"
                      danger
                      size="small"
                      @click="handleDelete(account)"
                    >
                      删除
                    </AButton>
                  </div>
                </ACard>
              </ACol>
            </ARow>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!loading" class="empty-state">
            <div class="empty-icon">👤</div>
            <div class="empty-text">暂无{{ currentPlatformName }}账号数据</div>
            <div class="empty-description">请先添加账号或切换其他平台查看</div>
          </div>
        </ASpin>

        <!-- 分页组件 -->
        <div v-if="pagination.total > 0" class="pagination-wrapper">
          <APagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="pagination.showSizeChanger"
            :show-quick-jumper="pagination.showQuickJumper"
            :show-total="pagination.showTotal"
            @change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 换组弹窗 -->
    <AModal
      v-model:visible="changeGroupModalVisible"
      :title="`账号换组 - ${currentChangeAccount?.account_name || ''}`"
      width="500px"
      :confirm-loading="changeGroupLoading"
      @ok="handleChangeGroupSubmit"
      @cancel="handleChangeGroupModalCancel"
    >
      <div v-if="currentChangeAccount" class="change-group-content">
        <div class="current-account-info">
          <AAvatar :src="currentChangeAccount.avatar" :size="40" />
          <div class="account-details">
            <div class="account-name">
              {{ currentChangeAccount.account_name }}
            </div>
            <div class="current-group">
              当前分组: {{ currentChangeAccount.account_group_name }}
            </div>
          </div>
        </div>

        <div class="group-selection">
          <div class="selection-label">选择目标分组:</div>
          <ASelect
            v-model:value="selectedGroupId"
            placeholder="请选择分组"
            style="width: 100%"
          >
            <ASelectOption
              v-for="group in availableGroups"
              :key="group.id"
              :value="group.id"
              :disabled="group.id === currentChangeAccount.account_group_id"
            >
              {{ group.name }}
              <span
                v-if="group.id === currentChangeAccount.account_group_id"
                class="current-tag"
              >
                (当前分组)
              </span>
            </ASelectOption>
          </ASelect>
        </div>
      </div>
    </AModal>

    <!-- 作品详情弹窗 -->
    <TaskDetailModal
      v-model:visible="worksModalVisible"
      :type="1"
      :channel-id="activeTab"
      :account-id="currentWorksAccount?.id.toString()"
    />
  </Page>
</template>

<style scoped lang="scss">
// 响应式设计
@media (max-width: 768px) {
  .account-management-container {
    padding: 16px;
  }

  // 账号卡片在移动端的调整
  .account-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .account-info-section {
    .account-name {
      font-size: 14px;
    }

    .account-group,
    .account-expire-time {
      font-size: 12px;
    }
  }
}

.account-management-container {
  min-height: 100vh;
  padding: 24px;
  background: var(--ant-color-bg-layout);
}

// 页面头部
.page-header {
  margin-bottom: 16px;
}

// 标签页头部
.tabs-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

// 平台标签页样式 - 与分组管理页面保持一致
.platform-tabs {
  flex: 1;

  :deep(.ant-tabs-tab) {
    font-size: 16px;
    font-weight: 500;

    &.ant-tabs-tab-active {
      font-weight: 600;
    }
  }

  :deep(.ant-tabs-content-holder) {
    display: none;
  }
}

// 头部操作按钮区域
.header-actions {
  display: flex;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.back-btn {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    transform: translateY(-1px);
  }
}

// 账号内容区域
.account-content {
  min-height: 400px;
}

// 账号卡片容器
.account-cards-container {
  margin-bottom: 32px;
}

// 账号卡片样式
.account-card {
  position: relative;
  height: 100%;
  border: 1px solid var(--ant-color-border);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--ant-color-primary-border);
    box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
    transform: translateY(-2px);
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
    background: var(--ant-color-bg-container);
  }
}

// 授权状态标识 - 右上角
.auth-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;

  :deep(.ant-badge-status-text) {
    font-size: 11px;
    font-weight: 500;
  }
}

// 账号头像区域
.account-avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;

  .account-avatar {
    border: 2px solid var(--ant-color-border);
  }
}

// 账号信息区域
.account-info-section {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
  text-align: center;

  .account-name {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
    color: var(--ant-color-text);
    white-space: nowrap;
  }

  .account-group {
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 13px;
    font-weight: 500;
    color: var(--ant-color-text-secondary);
    white-space: nowrap;
  }

  .account-expire-time {
    font-size: 12px;
    color: var(--ant-color-text-tertiary);
  }
}

// 账号操作按钮区域
.account-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid var(--ant-color-border);

  .ant-btn {
    flex: 1;
    max-width: 60px;
    height: 28px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 6px;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;

  .empty-icon {
    margin-bottom: 16px;
    font-size: 64px;
    opacity: 0.6;
  }

  .empty-text {
    margin-bottom: 8px;
    font-size: 18px;
    font-weight: 500;
    color: var(--ant-color-text-secondary);
  }

  .empty-description {
    font-size: 14px;
    color: var(--ant-color-text-tertiary);
  }
}

// 分页样式
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  border-top: 1px solid var(--ant-color-border);
}

// 深色主题适配
:deep(.ant-tabs-nav) {
  background: transparent;
}

// 换组弹窗样式
.change-group-content {
  .current-account-info {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px;
    margin-bottom: 20px;
    background: var(--ant-color-bg-container);
    border: 1px solid var(--ant-color-border);
    border-radius: 8px;

    .account-details {
      flex: 1;

      .account-name {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
      }

      .current-group {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
      }
    }
  }

  .group-selection {
    .selection-label {
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 500;
      color: var(--ant-color-text);
    }

    .current-tag {
      font-size: 12px;
      color: var(--ant-color-text-tertiary);
    }
  }
}
</style>
