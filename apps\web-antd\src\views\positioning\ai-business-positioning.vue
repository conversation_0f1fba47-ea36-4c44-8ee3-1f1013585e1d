<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
// 多语言导入已移除

import { Button, Card, Empty, message, TabPane, Tabs } from 'ant-design-vue';

import {
  getEnterpriseProjectApi,
  getOperationPlanUrl,
  getPlanSupervisionUrl,
  getProjectDataApi,
} from '#/api';

import { createConcurrentStreamManager } from '../../utils/stream-request';

// 多语言已移除

// 页面标题
const pageTitle = 'AI商业定位';
const pageDescription = '详细运营方案与AI企业运营督导报告';

// 报告数据
const operationPlanData = ref<string>('');
const planSupervisionData = ref<string>('');
const loading = ref(false);
const generating = ref(false);
const activeTab = ref('operation-plan');

// 计算是否有报告数据
const hasOperationPlan = computed(() => {
  return operationPlanData.value && operationPlanData.value.trim().length > 0;
});

const hasPlanSupervision = computed(() => {
  return (
    planSupervisionData.value && planSupervisionData.value.trim().length > 0
  );
});

const hasAnyData = computed(() => {
  return hasOperationPlan.value || hasPlanSupervision.value;
});

// 从API加载报告数据
const loadReportData = async () => {
  try {
    loading.value = true;

    // 直接从API获取最新数据
    const response = await getEnterpriseProjectApi();
    if (response) {
      if (response.operation_plan) {
        operationPlanData.value = response.operation_plan;
      }
      if (response.plan_supervision) {
        planSupervisionData.value = response.plan_supervision;
      }
    }
  } catch (error) {
    console.error('加载报告数据失败:', error);
    // 不显示错误消息，因为可能是正常的无数据状态
  } finally {
    loading.value = false;
  }
};

// 生成报告
const handleGenerateReports = async () => {
  try {
    generating.value = true;

    // 首先查询立项数据
    const projectResponse = await getProjectDataApi();

    if (!projectResponse) {
      message.warning('需要完成立项数据才可以生成报告');
      return;
    }

    // 准备生成报告的内容
    const content = formatProjectDataToContent(projectResponse);

    // 清空现有报告内容
    operationPlanData.value = '';
    planSupervisionData.value = '';

    // 开始生成运营方案报告
    await generateOperationPlan(content);
  } catch (error) {
    console.error('生成报告失败:', error);
    message.error('生成报告失败，请稍后重试');
    generating.value = false;
  }
};

// 生成运营方案报告
const generateOperationPlan = async (content: string) => {
  try {
    const streamManager = createConcurrentStreamManager(1);

    streamManager.addTask('operationPlan', {
      url: getOperationPlanUrl(),
      method: 'POST',
      body: {
        content,
      },
      timeout: 300_000, // 5分钟超时
      onData: (data: string) => {
        operationPlanData.value += data;
      },
      onComplete: () => {
        // 保存到localStorage
        localStorage.setItem(
          'enterprise_operation_plan',
          operationPlanData.value,
        );
        // 开始生成督导报告
        generatePlanSupervision(content);
      },
      onError: (error: Error) => {
        console.error('运营方案报告生成失败:', error);
        message.error(`运营方案报告生成失败: ${error.message}`);
        generating.value = false;
      },
    });
  } catch (error) {
    console.error('运营方案报告生成失败:', error);
    generating.value = false;
  }
};

// 生成督导报告
const generatePlanSupervision = async (content: string) => {
  try {
    const streamManager = createConcurrentStreamManager(1);

    streamManager.addTask('planSupervision', {
      url: getPlanSupervisionUrl(),
      method: 'POST',
      body: {
        content,
      },
      timeout: 300_000, // 5分钟超时
      onData: (data: string) => {
        planSupervisionData.value += data;
      },
      onComplete: () => {
        // 保存到localStorage
        localStorage.setItem(
          'enterprise_plan_supervision',
          planSupervisionData.value,
        );
        message.success('所有报告生成完成');
        generating.value = false;
      },
      onError: (error: Error) => {
        console.error('督导报告生成失败:', error);
        message.error(`督导报告生成失败: ${error.message}`);
        generating.value = false;
      },
    });
  } catch (error) {
    console.error('督导报告生成失败:', error);
    generating.value = false;
  }
};

// 格式化项目数据为内容字符串
const formatProjectDataToContent = (data: any): string => {
  const fields = [
    { label: '行业', value: data.industry },
    { label: '目标客户', value: data.target_customers },
    { label: '客户痛点', value: data.pain_points },
    { label: '行业优势', value: data.industry_advantages },
    { label: '解决方案需求', value: data.solution_requirements },
    { label: '客户自发推广', value: data.custom_promotion },
    { label: '私域用户总结', value: data.private_user_summary },
    { label: '年营业额', value: data.annual_revenue },
  ];

  return fields
    .filter((field) => field.value && field.value.trim())
    .map((field) => `${field.label}：${field.value}`)
    .join('，');
};

// 清空数据函数已移除

// 页面加载时获取数据
onMounted(() => {
  loadReportData();
});
</script>

<template>
  <Page :title="pageTitle" :description="pageDescription" auto-content-height>
    <div class="ai-business-positioning-container">
      <!-- 报告内容区域 -->
      <Card class="report-card" :loading="loading">
        <template #title>
          <div class="report-header">
            <div class="report-icon">🎯</div>
            <span class="report-title">AI商业定位报告</span>
            <div class="report-date">
              生成时间：{{ new Date().toLocaleString() }}
            </div>
          </div>
        </template>

        <template #extra>
          <Button
            type="primary"
            @click="handleGenerateReports"
            :loading="generating"
            class="generate-btn"
          >
            {{
              generating
                ? hasAnyData
                  ? '正在重新生成...'
                  : '正在生成报告...'
                : hasAnyData
                  ? '重新生成'
                  : '生成报告'
            }}
          </Button>
        </template>

        <!-- 有数据时显示标签页内容 -->
        <div v-if="hasAnyData">
          <Tabs v-model:active-key="activeTab" class="report-tabs">
            <TabPane key="operation-plan" tab="详细运营方案">
              <div v-if="hasOperationPlan" class="report-content">
                <div class="content-text">
                  {{ operationPlanData }}
                </div>
              </div>
              <div v-else class="tab-empty">
                <Empty
                  description="暂无运营方案数据"
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                >
                  <template #description>
                    <span class="empty-description">
                      点击上方"生成报告"按钮开始生成运营方案
                    </span>
                  </template>
                </Empty>
              </div>
            </TabPane>

            <TabPane key="plan-supervision" tab="AI企业运营督导">
              <div v-if="hasPlanSupervision" class="report-content">
                <div class="content-text">
                  {{ planSupervisionData }}
                </div>
              </div>
              <div v-else class="tab-empty">
                <Empty
                  description="暂无督导报告数据"
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                >
                  <template #description>
                    <span class="empty-description">
                      点击上方"生成报告"按钮开始生成督导报告
                    </span>
                  </template>
                </Empty>
              </div>
            </TabPane>
          </Tabs>
        </div>

        <!-- 完全无数据时显示空状态 -->
        <div v-else class="empty-state">
          <Empty
            description="暂无商业定位报告"
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
          >
            <template #description>
              <span class="empty-description">
                点击上方"生成报告"按钮开始生成AI商业定位报告
              </span>
            </template>
          </Empty>
        </div>
      </Card>
    </div>
  </Page>
</template>

<style scoped>
@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .report-date {
    font-size: 12px;
  }

  .content-text {
    font-size: 14px;
    line-height: 1.6;
  }

  .generate-btn {
    height: 28px;
    padding: 0 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .ai-business-positioning-container {
    padding: 12px;
  }
}

/* 打印样式 */
@media print {
  .action-bar {
    display: none;
  }

  .ai-business-positioning-container {
    padding: 0;
  }

  .report-card {
    border: none;
    box-shadow: none;
  }

  .content-text {
    padding: 0;
    background: none;
    border: none;
  }

  .report-tabs .ant-tabs-tab-btn {
    display: none;
  }
}

/* 主要布局样式 */
.ai-business-positioning-container {
  padding: 24px;
  background: hsl(var(--background));
}

/* 报告卡片样式 */
.report-card {
  min-height: 600px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
}

/* 生成按钮样式 */
.generate-btn {
  font-weight: 500;
  border-radius: 6px;
}

.report-card .ant-card-head {
  background: linear-gradient(
    135deg,
    hsl(var(--primary) / 5%) 0%,
    hsl(var(--background)) 100%
  );
  border-bottom: 2px solid hsl(var(--border));
}

/* 报告头部样式 */
.report-header {
  display: flex;
  gap: 16px;
  align-items: center;
}

.report-icon {
  font-size: 24px;
}

.report-title {
  font-size: 18px;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.report-date {
  margin-left: auto;
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 标签页样式 */
.report-tabs {
  margin-top: 16px;
}

.report-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.report-tabs .ant-tabs-content-holder {
  padding-top: 24px;
}

/* 报告内容样式 */
.report-content {
  padding: 0;
}

.content-text {
  height: calc(600px - 56px - 32px - 60px);
  padding: 16px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.8;
  color: hsl(var(--foreground));
  word-wrap: break-word;
  white-space: pre-wrap;
}

/* 标签页空状态样式 */
.tab-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px;
}

/* 完全空状态样式 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
}

.empty-description {
  font-size: 14px;
  color: hsl(var(--muted-foreground));
}

/* 隐藏所有滚动条 */
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.content-text::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* 响应式设计 */
</style>
