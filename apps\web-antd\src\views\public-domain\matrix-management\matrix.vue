<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { Page } from '@vben/common-ui';
import { useI18n } from '@vben/locales';

import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON> as ACard,
  Col as <PERSON>ol,
  List as AList,
  ListItem as AListItem,
  ListItemMeta as AListItemMeta,
  Modal as AModal,
  Progress as AProgress,
  Row as ARow,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tag as ATag,
  message,
} from 'ant-design-vue';

const { t } = useI18n();

// 类型定义
interface CountObj {
  account_count: number;
  douyin_account_count: number;
  kuaishou_account_count: number;
  shipinghao_account_count: number;
  xiaohong_account_count: number;
  exposure_count_total: number;
  like_count_total: number;
  comment_total: number;
}

interface TaskItem {
  id: number;
  name: string;
  channle_type: number;
  create_time: string;
  percentage: number;
  published_task: number;
  published_wait_task: number;
  published_fail_task: number;
  status: number; // 1: 进行中, 2: 已终止, 3: 已完成
}

interface TabItem {
  id: string;
  name: string;
}

// 状态管理
// const uid = ref('test'); // TODO: 替换为实际用户ID
const tabId = ref('');
const countObj = ref<CountObj>({
  account_count: 0,
  douyin_account_count: 0,
  kuaishou_account_count: 0,
  shipinghao_account_count: 0,
  xiaohong_account_count: 0,
  exposure_count_total: 0,
  like_count_total: 0,
  comment_total: 0,
});
const taskList = ref<TaskItem[]>([]);
// const router = useRouter();
const confirmModalVisible = ref(false);
const confirmModalTitle = ref('');
const confirmModalContent = ref('');
const currentTaskId = ref(0);
const actionType = ref(''); // 'delete' | 'terminate'

// 标签数据
const tabs: TabItem[] = [
  { id: '', name: '全部' },
  { id: '1', name: t('matrix-management.homepage.platforms.douyin') },
  { id: '2', name: t('matrix-management.homepage.platforms.kuaishou') },
  { id: '3', name: t('matrix-management.homepage.platforms.shipinghao') },
  { id: '4', name: t('matrix-management.homepage.platforms.xiaohong') },
];

// 平台统计数据格式化
const platformStats = [
  {
    label: t('matrix-management.homepage.stats.douyinAccount'),
    value: countObj.value.douyin_account_count,
  },
  {
    label: t('matrix-management.homepage.stats.kuaishouAccount'),
    value: countObj.value.kuaishou_account_count,
  },
  {
    label: t('matrix-management.homepage.stats.shipinghaoAccount'),
    value: countObj.value.shipinghao_account_count,
  },
  {
    label: t('matrix-management.homepage.stats.xiaohongAccount'),
    value: countObj.value.xiaohong_account_count,
  },
  {
    label: t('matrix-management.homepage.stats.totalExposure'),
    value: countObj.value.exposure_count_total,
  },
  {
    label: t('matrix-management.homepage.stats.totalLikes'),
    value: countObj.value.like_count_total,
  },
  {
    label: t('matrix-management.homepage.stats.totalComments'),
    value: countObj.value.comment_total,
  },
];

// 生命周期
onMounted(() => {
  // checkLoginStatus();
  getCountData();
  loadTaskList();
});

// 方法定义
// const checkLoginStatus = () => {
//   if (!uid.value) {
//     message.warning('请先登录');
//     router.push('/login');
//   }
// };

const getCountData = () => {
  // 模拟数据 TODO: 替换为实际API调用
  countObj.value = {
    account_count: 10,
    douyin_account_count: 3,
    kuaishou_account_count: 2,
    shipinghao_account_count: 1,
    xiaohong_account_count: 4,
    exposure_count_total: 1000,
    like_count_total: 500,
    comment_total: 200,
  };
};

const loadTaskList = () => {
  // 模拟数据 TODO: 替换为实际API调用
  taskList.value = [
    {
      id: 1,
      name: '任务1',
      channle_type: 1,
      create_time: '2023-10-01 10:00',
      percentage: 50,
      published_task: 2,
      published_wait_task: 1,
      published_fail_task: 1,
      status: 1,
    },
    {
      id: 2,
      name: '任务2',
      channle_type: 2,
      create_time: '2023-10-02 11:00',
      percentage: 100,
      published_task: 5,
      published_wait_task: 0,
      published_fail_task: 0,
      status: 3,
    },
    {
      id: 3,
      name: '任务3',
      channle_type: 3,
      create_time: '2023-10-03 12:00',
      percentage: 75,
      published_task: 3,
      published_wait_task: 1,
      published_fail_task: 1,
      status: 2,
    },
    {
      id: 4,
      name: '任务4',
      channle_type: 4,
      create_time: '2023-10-04 13:00',
      percentage: 25,
      published_task: 1,
      published_wait_task: 2,
      published_fail_task: 1,
      status: 1,
    },
  ];
};

const handleTabChange = (key: string) => {
  tabId.value = key;
  loadTaskList();
};

const getPlatformName = (type: number): string => {
  const platformMap: Record<number, string> = {
    1: 'D音',
    2: 'K手',
    3: '视频号',
    4: '小红薯',
  };
  return platformMap[type] || '未知平台';
};

const getPlatformColor = (type: number): string => {
  const colorMap: Record<number, string> = {
    1: '#166DFD',
    2: '#FF7B00',
    3: '#FFDC00',
    4: '#FF0000',
  };
  return colorMap[type] || '#ccc';
};

const getProgressStatus = (
  percent: number,
): 'active' | 'exception' | 'success' => {
  if (percent === 100) return 'success';
  return 'active';
};

const handleViewTask = (taskId: number, channelType: number) => {
  console.warn(`查看任务: ${taskId}, 平台: ${channelType}`);
  // TODO
  // router.push({
  //   path: '/public-domain',
  //   query: { taskId, type: 2, selLabel: channelType }
  // });
};

const handleDeleteTask = (taskId: number) => {
  currentTaskId.value = taskId;
  actionType.value = 'delete';
  confirmModalTitle.value = '提示';
  confirmModalContent.value = '是否确认删除该任务?';
  confirmModalVisible.value = true;
};

const handleTerminateTask = (taskId: number) => {
  currentTaskId.value = taskId;
  actionType.value = 'terminate';
  confirmModalTitle.value = '提示';
  confirmModalContent.value = '任务一旦终止后不可启用,是否确认?';
  confirmModalVisible.value = true;
};

const handleConfirmOk = () => {
  message.success('操作成功');
  loadTaskList(); // 重新加载任务列表
  confirmModalVisible.value = false;
};

const handleConfirmCancel = () => {
  confirmModalVisible.value = false;
};
</script>

<template>
  <Page>
    <!-- <a-layout class="layout"> -->
    <!-- <a-layout-content :style="{ padding: '0 24px', minHeight: '100vh', backgroundColor: '#232323', color: '#fff' }"> -->
    <!-- 统计卡片区域 -->
    <ACard
      class="stat-card"
      :style="{
        marginTop: '20px',
        background: 'linear-gradient(135deg, #166DFD 0%, #0F4C81 100%)',
        border: 'none',
        borderRadius: '10px',
        boxShadow: '0 4px 12px rgba(22, 109, 253, 0.47)',
      }"
    >
      <ARow :gutter="16">
        <ACol :span="24" class="mb-2">
          <div class="stat-label">
            {{ t('matrix-management.homepage.stats.totalPublishedAccounts') }}
          </div>
          <div class="stat-value">{{ countObj.account_count || 0 }}</div>
        </ACol>

        <ACol
          :span="6"
          v-for="(item, index) in platformStats"
          :key="index"
          class="platform-stat"
        >
          <div class="stat-label">{{ item.label }}</div>
          <div class="stat-value">{{ item.value }}</div>
        </ACol>
      </ARow>
    </ACard>

    <!-- 标签页切换 -->
    <ATabs v-model:active-key="tabId" class="mt-4" @change="handleTabChange">
      <ATabPane v-for="tab in tabs" :key="tab.id" :tab="tab.name" />
    </ATabs>

    <!-- 任务列表 -->
    <AList
      class="task-list mt-4"
      :data-source="taskList"
      :bordered="false"
      item-layout="horizontal"
    >
      <template #renderItem="{ item }">
        <AListItem
          :style="{
            borderRadius: '8px',
            marginBottom: '16px',
            padding: '16px',
          }"
        >
          <AListItemMeta>
            <template #title>
              <div class="task-title-meta-time">
                <div class="task-title-meta">
                  <div class="task-title">{{ item.name }}</div>
                  <ATag
                    :color="getPlatformColor(item.channle_type)"
                    class="task-meta"
                  >
                    {{ getPlatformName(item.channle_type) }}
                  </ATag>
                </div>
                <span class="task-time">{{ item.create_time }}</span>
              </div>
            </template>
          </AListItemMeta>

          <div class="task-content">
            <AProgress
              :percent="item.percentage"
              :status="getProgressStatus(item.percentage)"
              :style="{ width: '100%', marginBottom: '16px' }"
            />

            <ARow class="task-stats" :gutter="8">
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.success') }}
                </div>
              </ACol>
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_wait_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.waiting') }}
                </div>
              </ACol>
              <ACol :span="8" class="stat-item">
                <div class="stat-number">{{ item.published_fail_task }}</div>
                <div class="stat-desc">
                  {{ t('matrix-management.homepage.taskStatus.failed') }}
                </div>
              </ACol>
            </ARow>

            <div class="task-actions">
              <AButton
                v-if="[1, 2, 4].includes(item.channle_type)"
                type="link"
                @click="handleViewTask(item.id, item.channle_type)"
              >
                {{ t('matrix-management.homepage.actions.viewTask') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 1 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                @click="handleTerminateTask(item.id)"
              >
                {{ t('matrix-management.homepage.actions.terminateTask') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 3 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                disabled
              >
                {{ t('matrix-management.homepage.taskStatus.completed') }}
              </AButton>

              <AButton
                v-if="
                  item.status === 2 && [1, 2, 4].includes(item.channle_type)
                "
                type="link"
                disabled
              >
                {{ t('matrix-management.homepage.taskStatus.terminated') }}
              </AButton>

              <AButton
                :disabled="!(item.status === 2 || item.status === 3)"
                type="link"
                @click="handleDeleteTask(item.id)"
              >
                {{ t('matrix-management.homepage.actions.deleteTask') }}
              </AButton>
            </div>
          </div>
        </AListItem>
      </template>
    </AList>

    <!-- 返回按钮 -->
    <!-- <a-button 
          class="back-button"
          shape="circle"
          :icon="'ArrowLeftOutlined'"
          :style="{ position: 'fixed', bottom: '80px', right: '20px', backgroundColor: '#1B1B1B', borderColor: '#00FFCA', color: '#00FFCA' }"
          @click="handleBack"
        /> -->
    <!-- </a-layout-content> -->
    <!-- </a-layout> -->

    <!-- 确认对话框 -->
    <AModal
      v-model:visible="confirmModalVisible"
      :title="confirmModalTitle"
      @ok="handleConfirmOk"
      @cancel="handleConfirmCancel"
    >
      <template #default>
        <p>{{ confirmModalContent }}</p>
      </template>
      <template #footer>
        <AButton key="back" @click="handleConfirmCancel">
          {{ t('matrix-management.common.cancel') }}
        </AButton>
        <AButton key="submit" type="primary" @click="handleConfirmOk">
          {{ t('matrix-management.common.confirm') }}
        </AButton>
      </template>
    </AModal>
  </Page>
</template>

<style scoped lang="scss">
.stat-card {
  margin-bottom: 20px;

  .stat-label {
    margin-bottom: 4px;
    font-size: 14px;
  }

  .stat-value {
    font-size: 24px;
    font-weight: bold;
  }

  .platform-stat {
    padding: 10px 0;
    text-align: left;
  }
}

.task-list {
  .task-title-meta-time {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;

    .task-title-meta {
      display: flex;
      gap: 8px;
      align-items: center;
      margin-left: auto;
    }

    .task-title {
      font-size: 16px;
      font-weight: 500;
    }

    .task-meta {
      font-size: 12px;
    }

    .task-time {
      font-size: 12px;
    }
  }

  .task-content {
    width: 100%;
  }

  .task-stats {
    margin-top: 16px; // 添加顶部间距以便与进度条分离
    text-align: center;

    .stat-item {
      .stat-number {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: 500;
      }

      .stat-desc {
        font-size: 12px;
      }
    }
  }

  .task-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px; // 添加顶部间距以便与统计数据分离

    .ant-btn-link {
      margin-left: 10px;
    }
  }
}

.ant-progress {
  margin-bottom: 16px; // 确保进度条与统计数据之间有足够的间距
}

.back-button {
  position: fixed;
  right: 20px;
  bottom: 80px;
  z-index: 1000; // 确保返回按钮在最上层
  width: 48px;
  height: 48px;
}

.ant-tabs-bar {
  margin-bottom: 20px;
}

.ant-list-item {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

.ant-tag {
  margin-right: 8px;
}

/* 任务标题和元信息布局优化 */
.task-title-meta-time {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.task-title {
  max-width: 50%;
  // overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  font-weight: 500;
  white-space: nowrap;
}

.task-meta {
  margin-left: 8px;
}

.task-time {
  margin-left: auto;
  font-size: 12px;
  white-space: nowrap;
}
</style>
