<script setup lang="ts">
import { computed, onBeforeUnmount, ref } from 'vue';

import { VbenButton } from '@vben/common-ui';

import { Input } from 'ant-design-vue';

interface Props {
  modelValue?: string;
  codeLength?: number;
  placeholder?: string;
  disabled?: boolean;
  loading?: boolean;
  maxTime?: number;
  createText?: (countdown: number) => string;
  handleSendCode?: () => Promise<void>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  codeLength: 6,
  placeholder: '请输入验证码',
  disabled: false,
  loading: false,
  maxTime: 60,
  createText: (countdown: number) =>
    countdown > 0 ? `${countdown}s后重发` : '发送验证码',
  handleSendCode: async () => {},
});

const emit = defineEmits<{
  complete: [];
  sendError: [error: any];
  'update:modelValue': [value: string];
}>();

const timer = ref<ReturnType<typeof setTimeout>>();
const countdown = ref(0);

const btnText = computed(() => {
  return props.createText?.(countdown.value) || '';
});

const btnLoading = computed(() => {
  return props.loading || countdown.value > 0;
});

const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    // 只允许数字输入，并限制长度
    const numericValue = value.replaceAll(/\D/g, '').slice(0, props.codeLength);
    emit('update:modelValue', numericValue);

    // 如果输入完成，触发complete事件
    if (numericValue.length === props.codeLength) {
      emit('complete');
    }
  },
});

async function handleSend() {
  try {
    await props.handleSendCode?.();
    countdown.value = props.maxTime;
    startCountdown();
  } catch (error) {
    console.error('Failed to send code:', error);
    emit('sendError', error);
  }
}

function startCountdown() {
  if (countdown.value > 0) {
    timer.value = setTimeout(() => {
      countdown.value--;
      startCountdown();
    }, 1000);
  }
}

onBeforeUnmount(() => {
  countdown.value = 0;
  clearTimeout(timer.value);
});
</script>

<template>
  <div class="flex w-full gap-2">
    <Input
      v-model:value="inputValue"
      :disabled="disabled"
      :placeholder="placeholder"
      :maxlength="codeLength"
      class="flex-1"
      style="
        font-family: monospace;
        font-size: 16px;
        text-align: center;
        letter-spacing: 0.5em;
      "
      type="text"
      inputmode="numeric"
    />
    <VbenButton
      :disabled="disabled || btnLoading"
      :loading="btnLoading"
      class="flex-shrink-0"
      size="lg"
      variant="outline"
      @click="handleSend"
    >
      {{ btnText }}
    </VbenButton>
  </div>
</template>
