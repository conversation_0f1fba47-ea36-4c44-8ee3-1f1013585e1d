import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';

// 模拟用户信息数据 - 完整数据
const MOCK_USER_INFO_DATA_FULL = {
  site: '北京',
  industry: '互联网',
  experience: '5年',
  name: '张三',
  gender: '男',
  age: '30',
  company_name: '科技有限公司',
  customer: '企业客户',
  product: '软件产品',
  advantage: '技术领先',
  company_scale: '50人',
  turnover: '1000万',
};

// 模拟用户信息数据 - 部分数据（测试只填充有值字段）
const MOCK_USER_INFO_DATA_PARTIAL = {
  site: '上海',
  industry: '金融',
  name: '李四',
  gender: '女',
  company_name: '金融科技公司',
  // 其他字段为空或不存在，测试只填充有值的字段
};

export default defineEventHandler(async (event) => {
  // 验证用户身份
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  // 获取查询参数
  const { uid } = getQuery(event);

  // 验证必需参数
  if (!uid) {
    setResponseStatus(event, 400);
    return {
      errno: 400,
      data: null,
      message: '参数uid不能为空',
    };
  }

  // 将uid转换为数字
  const uidNumber = Number(uid);

  // 模拟根据uid获取数据
  // 这里可以根据需要返回不同的数据或null
  switch (uidNumber) {
    case 7: {
      // 返回完整数据的情况（JSON字符串格式）
      return {
        errno: 0,
        data: JSON.stringify(MOCK_USER_INFO_DATA_FULL),
        message: 'ok',
      };
    }
    case 8: {
      // 返回data为null的情况（用于测试静默处理）
      return {
        errno: 0,
        data: null,
        message: 'ok',
      };
    }
    case 9: {
      // 返回部分数据的情况（JSON字符串格式，测试只填充有值字段）
      return {
        errno: 0,
        data: JSON.stringify(MOCK_USER_INFO_DATA_PARTIAL),
        message: 'ok',
      };
    }
    case 10: {
      // 返回无效JSON字符串的情况（测试JSON解析错误处理）
      return {
        errno: 0,
        data: '{"invalid": json}', // 故意的无效JSON
        message: 'ok',
      };
    }
    default: {
      // 返回错误情况
      return {
        errno: 404,
        data: null,
        message: '未找到该用户的信息',
      };
    }
  }
});
