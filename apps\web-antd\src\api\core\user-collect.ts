import { requestClient } from '#/api/request';

/**
 * 用户收藏相关接口
 */

/**
 * 收藏列表请求参数接口
 */
export interface UserCollectListParams {
  category: number; // 收藏类型：1=文本，2=图片，3=视频
  page: number; // 页码
}

/**
 * 收藏列表项接口
 */
export interface UserCollectItem {
  category: number;
  content: string;
  cover: string;
  create_time: string;
  id: number;
  type: number;
  type_tile: string;
  uid: number;
  update_time: string;
}

/**
 * 收藏列表响应接口
 */
export interface UserCollectListResponse {
  list: UserCollectItem[];
  total: number;
  pindex: number;
  psize: number;
  totalPage: number;
}

/**
 * AI改写请求参数接口
 */
export interface AIRewriteParams {
  content: string; // 要改写的文本内容
  type: number; // 改写类型：1=标题，2=话题，3=文案
}

/**
 * 保存收藏更新参数接口
 */
export interface UpdateCollectParams {
  content: string; // 更新后的内容
  id: number; // 列表项的ID
}

/**
 * 删除收藏参数接口
 */
export interface DeleteCollectParams {
  id: number; // 要删除的收藏项ID
}

/**
 * 获取用户收藏列表
 * @param params 请求参数
 * @returns Promise<UserCollectListResponse>
 */
export function getUserCollectList(
  params: UserCollectListParams,
): Promise<UserCollectListResponse> {
  return requestClient.post('/mobile/user_collect/list', params);
}

/**
 * 获取AI改写流式接口URL
 * @returns string
 */
export function getAIRewriteUrl(): string {
  return '/mobile/ai_dialogue/spread';
}

/**
 * 更新收藏内容
 * @param params 更新参数
 * @returns Promise<any>
 */
export function updateUserCollect(params: UpdateCollectParams): Promise<any> {
  return requestClient.post('/mobile/user_collect/update', params);
}

/**
 * 删除收藏项
 * @param params 删除参数
 * @returns Promise<any>
 */
export function deleteUserCollect(params: DeleteCollectParams): Promise<any> {
  return requestClient.post('/mobile/user_collect/delete', params);
}
