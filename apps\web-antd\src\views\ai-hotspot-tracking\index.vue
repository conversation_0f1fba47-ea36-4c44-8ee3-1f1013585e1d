<script lang="ts" setup>
import type { HotspotApi } from '#/api/core/hotspot';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Button,
  Card,
  Empty,
  Input,
  List,
  message,
  Spin,
} from 'ant-design-vue';

import {
  getHotCopyUrl,
  getHotspotList,
  getInitialContent,
} from '#/api/core/hotspot';
import { createStreamHandler } from '#/utils/stream-request';

defineOptions({ name: 'AIHotspotTracking' });

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const hotspotList = ref<HotspotApi.HotspotItem[]>([]);
const selectedHotspot = ref<HotspotApi.HotspotItem | null>(null);
const textContent = ref('');
const isRewriting = ref(false);
const contentLoading = ref(false);

// 计算属性
const canGoToCreation = computed(() => {
  return textContent.value.trim().length > 0 && !isRewriting.value;
});

// 加载热点列表
const loadHotspotList = async () => {
  try {
    loading.value = true;
    const data = await getHotspotList();
    hotspotList.value = data;
  } catch (error) {
    console.error('获取热点列表失败:', error);
    message.error('获取热点列表失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 选择热点
const selectHotspot = async (hotspot: HotspotApi.HotspotItem) => {
  selectedHotspot.value = hotspot;
  await loadInitialContent(hotspot.word);
};

// 获取初始文案内容
const loadInitialContent = async (hotspotWord: string) => {
  try {
    contentLoading.value = true;
    const params: HotspotApi.GetInitialContentParams = {
      content: `请根据【${hotspotWord}】热点内容写1条长度500字以内的文案，不要附加话题，只生成纯文案。`,
      domainName: 'vapi.weijuyunke.com',
      ipStatus: 2,
      requestIp: '**************',
    };

    const content = await getInitialContent(params);
    textContent.value =
      content || '暂时无法获取文案内容，请手动输入或稍后重试。';
  } catch (error) {
    console.error('获取初始文案失败:', error);
    message.error('获取初始文案失败，请重试');
    textContent.value = '暂时无法获取文案内容，请手动输入或稍后重试。';
  } finally {
    contentLoading.value = false;
  }
};

// 一键仿写
const handleRewrite = async () => {
  if (!textContent.value.trim()) {
    message.warning('请先输入内容');
    return;
  }

  try {
    isRewriting.value = true;

    // 保存原始内容
    const originalContent = textContent.value;

    // 清空现有内容
    textContent.value = '';

    // 创建流式请求处理器
    const streamHandler = createStreamHandler();

    await streamHandler.request({
      url: getHotCopyUrl(),
      method: 'POST',
      body: {
        content: originalContent,
      },
      onData: (data: string) => {
        // 直接追加流式数据到文本内容中
        textContent.value += data;
      },
      onComplete: () => {
        message.success('仿写完成');
        isRewriting.value = false;
      },
      onError: (error: Error) => {
        console.error('仿写失败:', error);
        message.error(`仿写失败: ${error.message}`);
        isRewriting.value = false;
      },
    });
  } catch (error) {
    console.error('仿写失败:', error);
    message.error('仿写失败，请重试');
    isRewriting.value = false;
  }
};

// 去创作
const goToCreation = () => {
  if (!canGoToCreation.value) {
    return;
  }

  // 跳转到视频创作页面，并传递内容
  router.push({
    path: '/public-domain/video-creation',
    query: {
      content: textContent.value,
    },
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadHotspotList();
});
</script>

<template>
  <Page
    title="AI热点跟拍"
    description="基于热点内容快速生成创作文案"
    auto-content-height
  >
    <div class="hotspot-tracking-container">
      <div class="hotspot-layout">
        <!-- 左侧：热点列表区域 -->
        <div class="hotspot-list-section">
          <Card title="热点排行榜" class="hotspot-list-card">
            <template #extra>
              <Button type="text" size="small" @click="loadHotspotList">
                刷新
              </Button>
            </template>
            <Spin :spinning="loading">
              <div
                v-if="hotspotList.length === 0 && !loading"
                class="empty-state"
              >
                <Empty description="暂无热点数据">
                  <template #image>
                    <div style="font-size: 48px">🔥</div>
                  </template>
                  <Button type="primary" @click="loadHotspotList">
                    重新加载
                  </Button>
                </Empty>
              </div>
              <List v-else :data-source="hotspotList" class="hotspot-list">
                <template #renderItem="{ item, index }">
                  <List.Item
                    class="hotspot-item"
                    :class="[
                      {
                        'hotspot-item-selected':
                          selectedHotspot?.word === item.word,
                      },
                    ]"
                    @click="selectHotspot(item)"
                  >
                    <div class="hotspot-content">
                      <div class="hotspot-rank">{{ index + 1 }}</div>
                      <div class="hotspot-info">
                        <div class="hotspot-title">{{ item.word }}</div>
                        <div class="hotspot-heat">
                          热度: {{ item.hotindex }}
                        </div>
                      </div>
                      <Button type="primary" size="small" class="create-btn">
                        点击创作
                      </Button>
                    </div>
                  </List.Item>
                </template>
              </List>
            </Spin>
          </Card>
        </div>

        <!-- 右侧：创作区域 -->
        <div class="creation-section">
          <Card title="内容创作" class="creation-card">
            <div v-if="!selectedHotspot" class="no-selection">
              <Empty description="请先选择一个热点">
                <template #image>
                  <div style="font-size: 48px">👈</div>
                </template>
                <div class="selection-tips">
                  <p>选择左侧热点后，系统将自动生成相关文案</p>
                  <p>您可以编辑文案内容，或使用"一键仿写"功能优化文案</p>
                </div>
              </Empty>
            </div>
            <div v-else class="creation-content">
              <!-- 热点标题显示 -->
              <div class="selected-hotspot">
                <h3>当前热点：{{ selectedHotspot.word }}</h3>
              </div>

              <!-- 内容输入框 -->
              <div class="content-input-section">
                <Spin :spinning="contentLoading">
                  <Input.TextArea
                    v-model:value="textContent"
                    :placeholder="
                      isRewriting ? '正在生成内容...' : '文案内容将自动生成...'
                    "
                    :rows="12"
                    class="content-textarea"
                    :disabled="isRewriting"
                  />
                </Spin>
                <div v-if="isRewriting" class="rewriting-indicator">
                  <span class="typing-indicator">AI正在仿写中</span>
                  <span class="dots">...</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <Button
                  type="default"
                  :loading="isRewriting"
                  :disabled="!textContent.trim() || contentLoading"
                  @click="handleRewrite"
                  class="rewrite-btn"
                >
                  {{ isRewriting ? '正在仿写...' : '一键仿写' }}
                </Button>
                <Button
                  type="primary"
                  :disabled="!canGoToCreation || contentLoading"
                  @click="goToCreation"
                  class="go-creation-btn"
                >
                  去创作
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  </Page>
</template>

<style scoped>
@keyframes typing {
  0%,
  20% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hotspot-layout {
    flex-direction: column;
    height: auto;
  }

  .hotspot-list-section {
    flex: none;
    margin-bottom: 16px;
  }

  .hotspot-list-card {
    height: 400px;
  }

  .creation-section {
    flex: none;
  }
}

@media (max-width: 768px) {
  .hotspot-content {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons .ant-btn {
    width: 100%;
  }
}

.hotspot-tracking-container {
  padding: 16px;
}

.hotspot-layout {
  display: flex;
  gap: 16px;
  height: calc(100vh - 200px);
}

.hotspot-list-section {
  flex: 0 0 400px;
}

.creation-section {
  flex: 1;
}

.hotspot-list-card,
.creation-card {
  height: 100%;
}

.hotspot-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.hotspot-item {
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.hotspot-item:hover {
  background-color: #f5f5f5;
  border-color: #1890ff;
}

.hotspot-item-selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.hotspot-content {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}

.hotspot-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  min-width: 30px;
  height: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
  color: white;
  text-align: center;
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 50%;
}

.hotspot-info {
  flex: 1;
}

.hotspot-title {
  margin-bottom: 4px;
  font-weight: 500;
}

.hotspot-heat {
  font-size: 12px;
  color: #666;
}

.create-btn {
  flex-shrink: 0;
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.selection-tips {
  margin-top: 16px;
  line-height: 1.6;
  color: #666;
}

.selection-tips p {
  margin: 4px 0;
}

.creation-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.selected-hotspot {
  padding: 12px;
  margin-bottom: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

.selected-hotspot h3 {
  margin: 0;
  color: #52c41a;
}

.content-input-section {
  position: relative;
  flex: 1;
  margin-bottom: 16px;
}

.content-textarea {
  height: 100% !important;
  min-height: 300px;
}

.rewriting-indicator {
  position: absolute;
  right: 12px;
  bottom: 8px;
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  color: #1890ff;
  background: rgb(255 255 255 / 90%);
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

.typing-indicator {
  font-weight: 500;
}

.dots {
  animation: typing 1.5s infinite;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
