<script setup lang="ts">
import type {
  ProductVideoItem,
  ProductVideoListParams,
  RepostSettingResponse,
  RepostVideoParams,
} from '#/api/core/video-matrix';

import { ref, watch } from 'vue';

import { Button, message, Modal, Pagination } from 'ant-design-vue';

import {
  getProductVideoList,
  getRepostSetting,
  repostVideo,
} from '#/api/core/video-matrix';

// Props
interface Props {
  visible: boolean;
  productId: number;
  productTitle?: string;
  type: number; // 1表示从成品列表页面进入，2表示从素材列表进入
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
}

// 数据状态
const videoList = ref<ProductVideoItem[]>([]);
const loading = ref(false);
const repostSetting = ref<null | RepostSettingResponse>(null);

// 分页状态
const pagination = ref({
  current: 1,
  pageSize: 12,
  total: 0,
  totalPage: 0,
});

// 视频播放弹窗
const videoPlayerVisible = ref(false);
const currentVideoUrl = ref('');

// 转发相关状态
const platformSelectVisible = ref(false);
const qrcodeModalVisible = ref(false);
const currentQrcodeUrl = ref('');
const currentVideoForRepost = ref<null | ProductVideoItem>(null);
const repostLoading = ref(false);

// 加载视频列表
const loadVideoList = async (page: number = 1) => {
  try {
    loading.value = true;
    const params: ProductVideoListParams = {
      id: props.productId,
      page,
      psize: pagination.value.pageSize,
    };

    console.warn('获取成品视频列表，参数:', params);
    const response = await getProductVideoList(params);
    videoList.value = response.list;
    pagination.value = {
      current: response.pindex,
      pageSize: response.psize,
      total: response.total,
      totalPage: response.totalPage,
    };
    console.warn('成品视频列表获取成功:', response);
  } catch (error) {
    console.error('获取成品视频列表失败:', error);
    message.error('获取视频列表失败，请重试');
    videoList.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 加载转发设置
const loadRepostSetting = async () => {
  try {
    console.warn('获取转发设置');
    const response = await getRepostSetting();
    repostSetting.value = response;
    console.warn('转发设置获取成功:', response);
  } catch (error) {
    console.error('获取转发设置失败:', error);
    repostSetting.value = null;
  }
};

// 处理弹窗关闭
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理分页变化
const handlePageChange = (page: number) => {
  loadVideoList(page);
};

// 播放视频
const playVideo = (videoUrl: string) => {
  currentVideoUrl.value = videoUrl;
  videoPlayerVisible.value = true;
};

// 关闭视频播放弹窗
const closeVideoPlayer = () => {
  videoPlayerVisible.value = false;
  currentVideoUrl.value = '';
};

// 下载视频
const downloadVideo = async (videoUrl: string, title: string) => {
  try {
    message.loading('正在下载视频...', 1);

    // 使用fetch获取视频文件
    const response = await fetch(videoUrl);
    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `${title}.mp4`;
    link.style.display = 'none';

    document.body.append(link);
    link.click();
    link.remove();

    // 清理URL对象
    window.URL.revokeObjectURL(url);
    message.success('视频下载成功');
  } catch (error) {
    console.error('下载视频失败:', error);
    message.error('视频下载失败，请重试');
  }
};

// 转发功能
const handleRepost = (item: ProductVideoItem) => {
  currentVideoForRepost.value = item;
  platformSelectVisible.value = true;
};

// 选择平台转发
const handlePlatformSelect = async (way: number) => {
  if (!currentVideoForRepost.value) return;

  try {
    repostLoading.value = true;
    platformSelectVisible.value = false;

    const params: RepostVideoParams = {
      video_id: currentVideoForRepost.value.id,
      way, // 1表示抖音，2表示快手
      type: props.type, // 根据调用来源区分
    };

    console.warn('转发视频，参数:', params);
    const response = await repostVideo(params);

    currentQrcodeUrl.value = response;
    qrcodeModalVisible.value = true;

    message.success('转发二维码生成成功');
  } catch (error) {
    console.error('转发失败:', error);
    message.error('转发失败，请重试');
  } finally {
    repostLoading.value = false;
  }
};

// 下载二维码
const downloadQrcode = async () => {
  if (!currentQrcodeUrl.value) return;

  try {
    message.loading('正在下载二维码...', 1);

    const response = await fetch(currentQrcodeUrl.value);
    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `转发二维码_${Date.now()}.png`;
    link.style.display = 'none';

    document.body.append(link);
    link.click();
    link.remove();

    window.URL.revokeObjectURL(url);
    message.success('二维码下载成功');
  } catch (error) {
    console.error('下载二维码失败:', error);
    message.error('二维码下载失败，请重试');
  }
};

// 查看数据功能
const handleViewData = (item: ProductVideoItem) => {
  console.warn('查看数据:', item);
  message.info('查看数据功能开发中...');
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && props.productId) {
      // 弹窗打开时加载数据
      loadVideoList(1);
      loadRepostSetting();
    }
  },
  { immediate: true },
);

// 监听产品ID变化
watch(
  () => props.productId,
  (newProductId) => {
    if (newProductId && props.visible) {
      loadVideoList(1);
    }
  },
);
</script>

<template>
  <Modal
    :open="visible"
    :title="`${productTitle || '成品'} - 视频列表`"
    width="1000px"
    :footer="null"
    @cancel="handleCancel"
    class="product-video-modal"
  >
    <div class="video-list-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <span class="loading-text">正在加载视频列表...</span>
      </div>

      <!-- 无数据提示 -->
      <div v-else-if="videoList.length === 0" class="empty-container">
        <div class="empty-text">暂无视频数据</div>
      </div>

      <!-- 视频列表 -->
      <div v-else class="video-grid">
        <div v-for="video in videoList" :key="video.id" class="video-item">
          <!-- 视频封面 -->
          <div class="video-thumbnail" @click="playVideo(video.complete_video)">
            <img
              :src="video.complete_cover"
              :alt="video.title"
              class="thumbnail-image"
            />
            <!-- 播放图标 -->
            <div class="play-icon-overlay">
              <img
                src="https://szr.jiajs.cn/index/158.png"
                alt="播放"
                class="play-icon"
              />
            </div>
          </div>

          <!-- 视频信息 -->
          <div class="video-info">
            <div class="video-title" :title="video.title">
              {{ video.title }}
            </div>
            <div class="video-time">
              {{ video.create_time }}
            </div>
          </div>

          <!-- 操作按钮 - 只有type为1时才显示 -->
          <div v-if="type === 1" class="video-actions">
            <!-- 转发按钮 - 根据设置显示 -->
            <Button
              v-if="repostSetting?.is_open === 1"
              type="primary"
              size="small"
              @click="handleRepost(video)"
            >
              转发
            </Button>

            <!-- 查看数据按钮 - 根据设置显示 -->
            <Button
              v-if="repostSetting?.is_open === 1"
              type="default"
              size="small"
              @click="handleViewData(video)"
            >
              查看数据
            </Button>

            <!-- 下载按钮 - 默认显示 -->
            <Button
              type="default"
              size="small"
              @click="downloadVideo(video.complete_video, video.title)"
            >
              下载
            </Button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-container">
        <Pagination
          v-model:current="pagination.current"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :show-size-changer="false"
          :show-quick-jumper="true"
          :show-total="
            (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          "
          @change="handlePageChange"
        />
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <Modal
      v-model:open="videoPlayerVisible"
      title="视频播放"
      width="600px"
      :footer="null"
      @cancel="closeVideoPlayer"
      class="video-player-modal"
    >
      <div class="video-player-container">
        <video
          v-if="currentVideoUrl"
          :src="currentVideoUrl"
          controls
          autoplay
          class="video-player"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </Modal>

    <!-- 平台选择弹窗 -->
    <Modal
      v-model:open="platformSelectVisible"
      title="选择转发平台"
      width="400px"
      :footer="null"
      @cancel="() => (platformSelectVisible = false)"
      class="platform-select-modal"
    >
      <div class="platform-select-container">
        <div class="platform-buttons">
          <Button
            type="primary"
            size="large"
            :loading="repostLoading"
            @click="handlePlatformSelect(1)"
            class="platform-btn douyin-btn"
          >
            抖音
          </Button>
          <Button
            type="primary"
            size="large"
            :loading="repostLoading"
            @click="handlePlatformSelect(2)"
            class="platform-btn kuaishou-btn"
          >
            快手
          </Button>
        </div>
      </div>
    </Modal>

    <!-- 二维码显示弹窗 -->
    <Modal
      v-model:open="qrcodeModalVisible"
      title="转发二维码"
      width="400px"
      :footer="null"
      @cancel="() => (qrcodeModalVisible = false)"
      class="qrcode-modal"
    >
      <div class="qrcode-container">
        <div class="qrcode-image-wrapper">
          <img
            v-if="currentQrcodeUrl"
            :src="currentQrcodeUrl"
            alt="转发二维码"
            class="qrcode-image"
          />
        </div>
        <div class="qrcode-footer">
          <Button type="primary" size="large" @click="downloadQrcode" block>
            保存图片
          </Button>
        </div>
      </div>
    </Modal>
  </Modal>
</template>

<style lang="scss" scoped>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.product-video-modal {
  .video-list-container {
    max-height: 600px;
    overflow-y: auto;
  }
}

.loading-container {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f0f0f0;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
}

.empty-text {
  font-size: 16px;
  color: #999;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

.video-item {
  overflow: hidden;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 12px rgb(24 144 255 / 15%);
    transform: translateY(-2px);
  }
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 177.78%; /* 9:16 比例 (16/9 * 100%) */
  overflow: hidden;
  cursor: pointer;
}

.thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.video-thumbnail:hover .thumbnail-image {
  transform: scale(1.05);
}

.play-icon-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0.8;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease;
}

.video-thumbnail:hover .play-icon-overlay {
  opacity: 1;
}

.play-icon {
  width: 40px;
  height: 40px;
}

.video-info {
  padding: 12px;
}

.video-title {
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.video-time {
  font-size: 12px;
  color: #999;
}

.video-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 0 12px 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.video-player-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  width: 100%;
  max-width: 500px;
  height: auto;
  max-height: 400px;
}

/* 平台选择弹窗样式 */
.platform-select-container {
  padding: 20px 0;
}

.platform-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.platform-btn {
  width: 120px;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
}

.douyin-btn {
  background: #fe2c55;
  border-color: #fe2c55;

  &:hover {
    background: #ff4569;
    border-color: #ff4569;
  }
}

.kuaishou-btn {
  background: #f60;
  border-color: #f60;

  &:hover {
    background: #ff7a1a;
    border-color: #ff7a1a;
  }
}

/* 二维码弹窗样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.qrcode-image-wrapper {
  margin-bottom: 20px;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
}

.qrcode-footer {
  width: 100%;
}
</style>
