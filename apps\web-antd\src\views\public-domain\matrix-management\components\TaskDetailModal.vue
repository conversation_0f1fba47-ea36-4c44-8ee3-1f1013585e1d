<script setup lang="ts">
import type {
  DouyinCommentItem,
  DouyinCommentListParams,
  PublishParams,
  VideoTaskDetailItem,
  VideoTaskDetailListParams,
  VideoTaskStatisticsParams,
  VideoTaskStatisticsResponse,
} from '#/api/core';

import { computed, onMounted, ref, watch } from 'vue';

import {
  Avatar as AAvatar,
  Button as AButton,
  Col as ACol,
  Modal as AModal,
  Pagination as APagination,
  Row as ARow,
  Spin as ASpin,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tag as ATag,
  message,
} from 'ant-design-vue';

import {
  getDouyinCommentList,
  getVideoTaskDetailList,
  getVideoTaskStatistics,
  publishDouyin,
  publishKuaishou,
  publishXiaohongshu,
} from '#/api/core';

// Props 接口定义
interface Props {
  visible: boolean;
  type: 1 | 2; // 1=账户详情, 2=任务详情
  channelId: number; // 渠道ID
  taskId?: string; // 任务ID（type=2时必传）
  accountId?: string; // 账户ID（type=1时必传）
}

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

// Emits
interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'action', action: string, item: any): void;
}

// 响应式数据
const statistics = ref<VideoTaskStatisticsResponse>({
  comment: 0,
  exposure_count: 0,
  like_count: 0,
  published_fail_task: 0,
  published_task: 0,
  published_wait_task: 0,
  totle_task: 0,
});

const activeTab = ref('');
const statisticsLoading = ref(false);
const listLoading = ref(false);
const taskList = ref<VideoTaskDetailItem[]>([]);

// 视频播放弹窗状态
const videoModalVisible = ref(false);
const currentVideoSrc = ref('');

// 评论弹窗状态
const commentModalVisible = ref(false);
const commentLoading = ref(false);
const commentList = ref<DouyinCommentItem[]>([]);
const currentVideoItem = ref<null | VideoTaskDetailItem>(null);
const commentPagination = ref({
  cursor: 0,
  count: 12,
  hasMore: false,
});

// 发布状态管理
const publishingItems = ref<Set<number>>(new Set());
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 筛选标签页数据
const tabs = [
  { id: '', name: '全部' },
  { id: '1', name: '待发布' },
  { id: '3', name: '发布成功' },
  { id: '4', name: '发布失败' },
];

// 计算属性
const modalTitle = computed(() => {
  return props.type === 1 ? '账户详情' : '任务详情';
});

// 格式化数字显示
const formatNumber = (num: number) => {
  if (num >= 10_000) {
    return `${(num / 10_000).toFixed(1)}万`;
  }
  return num.toLocaleString();
};

// 格式化评论时间
const formatCommentTime = (timestamp: number) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 加载更多评论
const loadMoreComments = () => {
  if (currentVideoItem.value && commentPagination.value.hasMore) {
    loadCommentList(currentVideoItem.value, true);
  }
};

// 发布视频
const publishVideo = async (item: VideoTaskDetailItem) => {
  try {
    publishingItems.value.add(item.id);

    const params: PublishParams = {
      uid: 7,
      id: item.id,
    };

    // 根据平台类型选择对应的发布接口
    let publishFunction;
    let platformName = '';

    switch (item.channle_type) {
      case 1: {
        // 抖音
        publishFunction = publishDouyin;
        platformName = '抖音';
        break;
      }
      case 2: {
        // 快手
        publishFunction = publishKuaishou;
        platformName = '快手';
        break;
      }
      case 4: {
        // 小红书
        publishFunction = publishXiaohongshu;
        platformName = '小红书';
        break;
      }
      default: {
        throw new Error('不支持的平台类型');
      }
    }

    await publishFunction(params);

    // 发布成功提示
    message.success(`${platformName}视频发布成功！`);

    // 刷新列表数据
    loadListData();
  } catch (error) {
    console.error('发布视频失败:', error);
    message.error('发布视频失败，请重试');
  } finally {
    publishingItems.value.delete(item.id);
  }
};

// 获取状态颜色
const getStatusColor = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'orange', // 待发布
    2: 'orange', // 待发布
    3: 'green', // 发布成功
    4: 'red', // 发布失败
    5: 'orange', // 待发布
    6: 'orange', // 待发布
    7: 'gray', // 任务终止
  };
  return statusMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待发布',
    2: '待发布',
    3: '发布成功',
    4: '发布失败',
    5: '待发布',
    6: '待发布',
    7: '任务终止',
  };
  return statusMap[status] || '未知状态';
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    statisticsLoading.value = true;
    const params: VideoTaskStatisticsParams = {};

    if (props.type === 2 && props.taskId) {
      params.id = props.taskId;
    } else if (props.type === 1 && props.accountId) {
      params.three_account_id = props.accountId;
    }

    const response = await getVideoTaskStatistics(params);
    statistics.value = response;
  } catch (error) {
    console.error('加载统计数据失败:', error);
    // 保持默认值，不影响用户体验
  } finally {
    statisticsLoading.value = false;
  }
};

// 加载列表数据
const loadListData = async () => {
  try {
    listLoading.value = true;

    const params: VideoTaskDetailListParams = {
      task_id: props.type === 2 ? props.taskId || '' : '',
      three_account_id: props.type === 1 ? props.accountId || '' : '', // 修正：type=1时也传taskId
      status: activeTab.value,
      type: props.channelId,
      page: pagination.value.current,
      limit: pagination.value.pageSize,
    };

    const response = await getVideoTaskDetailList(params);
    taskList.value = response.list;
    pagination.value.total = response.total;
  } catch (error) {
    console.error('加载列表数据失败:', error);
    taskList.value = [];
    pagination.value.total = 0;
    // 可以在这里添加用户友好的错误提示
    // message.error('加载数据失败，请重试');
  } finally {
    listLoading.value = false;
  }
};

// 处理标签页切换
const handleTabChange = (key: number | string) => {
  activeTab.value = String(key);
  pagination.value.current = 1;
  loadListData();
};

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.value.current = page;
  pagination.value.pageSize = pageSize;
  loadListData();
};

// 加载评论列表
const loadCommentList = async (
  videoItem: VideoTaskDetailItem,
  isLoadMore = false,
) => {
  try {
    commentLoading.value = true;

    const params: DouyinCommentListParams = {
      id: videoItem.id,
      uid: 7,
      cursor: isLoadMore ? commentPagination.value.cursor : 0,
      count: commentPagination.value.count,
    };

    const response = await getDouyinCommentList(params);

    // 根据是否加载更多决定处理方式
    commentList.value = isLoadMore
      ? [...commentList.value, ...(response.comments || [])] // 加载更多时追加到现有列表
      : response.comments || []; // 首次加载时替换列表

    commentPagination.value.cursor = response.cursor;
    commentPagination.value.hasMore = response.has_more === 1;
  } catch (error) {
    console.error('加载评论列表失败:', error);
    if (!isLoadMore) {
      commentList.value = [];
    }
  } finally {
    commentLoading.value = false;
  }
};

// 处理操作
const handleAction = (action: string, item: VideoTaskDetailItem) => {
  switch (action) {
    case 'publish':
    case 'republish': {
      // 立即发布或立即补发
      publishVideo(item);

      break;
    }
    case 'viewComments': {
      // 打开评论弹窗
      currentVideoItem.value = item;
      commentModalVisible.value = true;
      loadCommentList(item);

      break;
    }
    case 'viewWork': {
      // 打开视频播放弹窗
      currentVideoSrc.value = item.video_src;
      videoModalVisible.value = true;

      break;
    }
    default: {
      // 其他操作传递给父组件
      emit('action', action, item);
    }
  }
};

// 重置状态
const resetState = () => {
  activeTab.value = '';
  pagination.value.current = 1;
  taskList.value = [];
  publishingItems.value.clear();
  statistics.value = {
    comment: 0,
    exposure_count: 0,
    like_count: 0,
    published_fail_task: 0,
    published_task: 0,
    published_wait_task: 0,
    totle_task: 0,
  };
};

// 关闭视频播放弹窗
const closeVideoModal = () => {
  videoModalVisible.value = false;
  currentVideoSrc.value = '';
};

// 关闭评论弹窗
const closeCommentModal = () => {
  commentModalVisible.value = false;
  commentList.value = [];
  currentVideoItem.value = null;
  commentPagination.value.cursor = 0;
  commentPagination.value.hasMore = false;
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  resetState();
  // 同时关闭子弹窗
  closeVideoModal();
  closeCommentModal();
};

// 监听visible变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      loadStatistics();
      loadListData();
    }
  },
);

// 组件挂载时初始化
onMounted(() => {
  if (props.visible) {
    loadStatistics();
    loadListData();
  }
});
</script>

<template>
  <AModal
    :visible="visible"
    :title="modalTitle"
    width="1200px"
    :footer="null"
    @cancel="handleClose"
    class="task-detail-modal"
    :body-style="{ maxHeight: '80vh', overflow: 'auto' }"
  >
    <!-- 顶部统计数据卡片 -->
    <div class="statistics-section">
      <ASpin :spinning="statisticsLoading">
        <ARow :gutter="16">
          <ACol :span="4">
            <div class="stat-card success">
              <div class="stat-number">
                {{ statistics.published_task || 0 }}
              </div>
              <div class="stat-label">发布成功</div>
            </div>
          </ACol>
          <ACol :span="4">
            <div class="stat-card waiting">
              <div class="stat-number">
                {{ statistics.published_wait_task || 0 }}
              </div>
              <div class="stat-label">待发布</div>
            </div>
          </ACol>
          <ACol :span="4">
            <div class="stat-card failed">
              <div class="stat-number">
                {{ statistics.published_fail_task || 0 }}
              </div>
              <div class="stat-label">发布失败</div>
            </div>
          </ACol>
          <ACol :span="4">
            <div class="stat-card exposure">
              <div class="stat-number">
                {{ formatNumber(statistics.exposure_count || 0) }}
              </div>
              <div class="stat-label">曝光量</div>
            </div>
          </ACol>
          <ACol :span="4">
            <div class="stat-card like">
              <div class="stat-number">
                {{ formatNumber(statistics.like_count || 0) }}
              </div>
              <div class="stat-label">点赞量</div>
            </div>
          </ACol>
          <ACol :span="4">
            <div class="stat-card comment">
              <div class="stat-number">
                {{ formatNumber(statistics.comment || 0) }}
              </div>
              <div class="stat-label">评论数</div>
            </div>
          </ACol>
        </ARow>
      </ASpin>
    </div>

    <!-- 中部筛选标签页 -->
    <div class="filter-section">
      <ATabs v-model:active-key="activeTab" @change="handleTabChange">
        <ATabPane v-for="tab in tabs" :key="tab.id" :tab="tab.name" />
      </ATabs>
    </div>

    <!-- 底部列表卡片区域 -->
    <div class="list-section">
      <ASpin :spinning="listLoading">
        <div class="task-list-container">
          <div v-for="item in taskList" :key="item.id" class="task-list-card">
            <!-- 卡片上部分 -->
            <div class="card-header">
              <div class="user-info">
                <AAvatar
                  :src="item.avatar"
                  :alt="item.account_name"
                  size="small"
                />
                <span class="username">{{ item.account_name }}</span>
              </div>
              <div class="publish-info">
                <span class="publish-time">{{ item.release_time }}</span>
                <ATag :color="getStatusColor(item.status)" class="status-tag">
                  {{ getStatusText(item.status) }}
                </ATag>
              </div>
            </div>

            <!-- 卡片下部分 - 根据状态动态显示 -->
            <div class="card-content">
              <!-- status=3时显示数据和操作按钮 -->
              <template v-if="item.status === 3">
                <div class="data-stats">
                  <span class="data-item"
                    >曝光: {{ formatNumber(item.exposure_count || 0) }}</span
                  >
                  <span class="data-item"
                    >点赞: {{ formatNumber(item.like_count || 0) }}</span
                  >
                  <span class="data-item"
                    >评论: {{ formatNumber(item.comment || 0) }}</span
                  >
                </div>
                <div class="action-buttons">
                  <!-- 抖音平台显示查看评论和查看作品 -->
                  <template v-if="channelId === 1">
                    <AButton
                      size="small"
                      @click="handleAction('viewComments', item)"
                    >
                      查看评论
                    </AButton>
                    <AButton
                      size="small"
                      @click="handleAction('viewWork', item)"
                    >
                      查看作品
                    </AButton>
                  </template>
                  <!-- 快手和小红书平台只显示查看作品 -->
                  <template v-else-if="[2, 4].includes(channelId)">
                    <AButton
                      size="small"
                      @click="handleAction('viewWork', item)"
                    >
                      查看作品
                    </AButton>
                  </template>
                </div>
              </template>

              <!-- status=1时显示立即发布按钮 -->
              <template v-else-if="item.status === 1">
                <div class="action-buttons">
                  <AButton
                    type="primary"
                    size="small"
                    :loading="publishingItems.has(item.id)"
                    @click="handleAction('publish', item)"
                  >
                    立即发布
                  </AButton>
                </div>
              </template>

              <!-- status=4时显示失败原因和立即补发按钮 -->
              <template v-else-if="item.status === 4">
                <div class="failure-info">
                  <div class="failure-reason">
                    失败原因: {{ item.hint || '未知原因' }}
                  </div>
                  <div class="action-buttons">
                    <AButton
                      type="primary"
                      size="small"
                      :loading="publishingItems.has(item.id)"
                      @click="handleAction('republish', item)"
                    >
                      立即补发
                    </AButton>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <APagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :show-total="
              (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            "
            @change="handlePageChange"
          />
        </div>
      </ASpin>
    </div>

    <!-- 视频播放弹窗 -->
    <AModal
      v-model:visible="videoModalVisible"
      title="视频播放"
      width="800px"
      :footer="null"
      @cancel="closeVideoModal"
    >
      <div class="video-player-container">
        <video
          v-if="currentVideoSrc"
          :src="currentVideoSrc"
          controls
          autoplay
          class="video-player"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </AModal>

    <!-- 评论列表弹窗 -->
    <AModal
      v-model:visible="commentModalVisible"
      title="评论列表"
      width="600px"
      :footer="null"
      @cancel="closeCommentModal"
    >
      <div class="comment-modal-content">
        <ASpin :spinning="commentLoading">
          <div
            v-if="commentList.length === 0 && !commentLoading"
            class="empty-comments"
          >
            <div class="empty-text">暂无评论</div>
          </div>
          <div v-else class="comment-list">
            <div
              v-for="(comment, index) in commentList"
              :key="index"
              class="comment-item"
            >
              <div class="comment-header">
                <AAvatar
                  :src="
                    comment.user.avatar_thumb.url_list[4] ||
                    comment.user.avatar_thumb.url_list[0]
                  "
                  :alt="comment.user.nickname"
                  size="small"
                />
                <div class="comment-user-info">
                  <div class="comment-username">
                    {{ comment.user.nickname }}
                  </div>
                  <div class="comment-time">
                    {{ formatCommentTime(comment.create_time) }}
                  </div>
                </div>
              </div>
              <div class="comment-content">{{ comment.text }}</div>
            </div>
          </div>

          <!-- 加载更多按钮 -->
          <div v-if="commentPagination.hasMore" class="load-more-container">
            <AButton @click="loadMoreComments" :loading="commentLoading">
              加载更多评论
            </AButton>
          </div>
        </ASpin>
      </div>
    </AModal>
  </AModal>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .task-list-container {
    grid-template-columns: 1fr;
  }

  .data-stats {
    flex-direction: column;
    gap: 4px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .video-player {
    max-height: 300px;
  }

  .comment-modal-content {
    max-height: 400px;
  }

  .comment-content {
    margin-top: 8px;
    margin-left: 0;
  }
}

.task-detail-modal {
  .ant-modal-body {
    padding: 24px;
  }
}

.statistics-section {
  margin-bottom: 24px;
}

.stat-card {
  padding: 16px;
  text-align: center;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
}

.stat-number {
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-card.success .stat-number {
  color: #52c41a;
}

.stat-card.waiting .stat-number {
  color: #1890ff;
}

.stat-card.failed .stat-number {
  color: #ff4d4f;
}

.stat-card.exposure .stat-number {
  color: #722ed1;
}

.stat-card.like .stat-number {
  color: #eb2f96;
}

.stat-card.comment .stat-number {
  color: #13c2c2;
}

.filter-section {
  margin-bottom: 24px;
}

.list-section {
  min-height: 400px;
}

.task-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.task-list-card {
  padding: 16px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.username {
  font-weight: 500;
  color: #1a1a1a;
}

.publish-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.publish-time {
  font-size: 12px;
  color: #999;
}

.status-tag {
  margin: 0;
}

.card-content {
  min-height: 40px;
}

.data-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.data-item {
  font-size: 12px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.failure-info {
  .failure-reason {
    margin-bottom: 8px;
    font-size: 12px;
    color: #ff4d4f;
  }
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 视频播放器样式 */
.video-player-container {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #000;
  border-radius: 8px;
}

.video-player {
  width: 100%;
  max-height: 500px;
  outline: none;
}

/* 评论弹窗样式 */
.comment-modal-content {
  max-height: 500px;
  overflow-y: auto;
}

.empty-comments {
  padding: 40px 20px;
  color: #999;
  text-align: center;
}

.empty-text {
  font-size: 14px;
}

.comment-list {
  padding: 0;
}

.comment-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.comment-header {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 8px;
}

.comment-user-info {
  flex: 1;
}

.comment-username {
  margin-bottom: 2px;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  margin-left: 44px; /* 对齐头像右侧 */
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.load-more-container {
  padding: 16px 0;
  margin-top: 16px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
}
</style>
