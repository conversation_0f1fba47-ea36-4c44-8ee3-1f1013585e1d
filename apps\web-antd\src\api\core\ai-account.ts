import { requestClient } from '#/api/request';

export namespace AIAccountApi {
  /** AI账号信息接口 */
  export interface AIAccountInfo {
    content: {
      des: string;
      name: string;
      top_video_content: string[];
    };
    des: string;
    name: string;
    top_video_content: string[];
    create_time: string;
    id: number;
    uid: number;
    update_time: string;
  }

  /** 更新AI账号信息参数接口 */
  export interface UpdateAIAccountParams {
    content: string; // JSON字符串格式
  }

  /** AI账号重新生成返回数据接口 */
  export interface AIAccountGenerateResult {
    content_direction: string[];
    creative_suggestion: string[];
    des: string;
    name: string;
    personality: string;
    top_video_content: string[];
  }

  /** 账号列表请求参数接口 */
  export interface AccountListParams {
    page: number; // 页码
    psize: number; // 每页数量，默认12
    type: string; // 平台类型：1抖音2快手3视频号4小红书
  }

  /** 账号数据项接口 */
  export interface AccountItem {
    access_token: null | string;
    account_group_id: number;
    account_group_name: string;
    account_name: string;
    avatar: string;
    city_id: number;
    comment: number;
    cookie: string;
    create_time: string;
    customize_account_avatar: null | string;
    customize_account_name: null | string;
    delete_time: null | string;
    douyin_uid: string;
    expires_in: string;
    exposure_count: number;
    id: number;
    is_delete: number;
    like_count: number;
    open_id: null | string;
    percentage: string;
    province_id: number;
    published_task: number;
    qrcode_id: number;
    red_id: null | string;
    refresh_token: null | string;
    refresh_token_expires_in: null | string;
    released_task: number;
    status: number;
    totle_task: number;
    type: number;
    uid: number;
    update_time: string;
  }

  /** 账号列表响应接口 */
  export interface AccountListResponse {
    pindex: number;
    psize: number;
    total: number;
    totalPage: number;
    list: AccountItem[];
  }

  /** 删除账号请求参数接口 */
  export interface DelAccountParams {
    id: number[]; // 账号ID数组
  }

  /** 账号换组请求参数接口 */
  export interface AccountExchangeGroupParams {
    id: number[]; // 账号ID数组
    account_group_id: number; // 目标分组ID
  }
}

/**
 * 获取AI账号信息
 */
export async function getAIAccountInfoApi() {
  return requestClient.get<AIAccountApi.AIAccountInfo>(
    '/mobile/ai_account/getInfo',
  );
}

/**
 * 更新AI账号信息
 */
export async function updateAIAccountInfoApi(
  params: AIAccountApi.UpdateAIAccountParams,
) {
  return requestClient.post<any>('/mobile/ai_account/update', params);
}

/**
 * AI账号重新生成
 */
export async function generateAIAccountApi() {
  return requestClient.post<AIAccountApi.AIAccountGenerateResult>(
    '/mobile/ai_text/account',
    undefined,
    {
      timeout: 30_000, // 30秒超时
    },
  );
}

/**
 * 获取账号列表
 * @param params 请求参数
 * @returns Promise<AIAccountApi.AccountListResponse>
 */
export async function getAccountListApi(
  params: AIAccountApi.AccountListParams,
): Promise<AIAccountApi.AccountListResponse> {
  return requestClient.post('/mobile/externalAccount/accountList', params);
}

/**
 * 删除账号
 * @param params 请求参数
 * @returns Promise<any>
 */
export async function delAccountApi(
  params: AIAccountApi.DelAccountParams,
): Promise<any> {
  return requestClient.post('/mobile/externalAccount/delAccount', params);
}

/**
 * 账号换组
 * @param params 请求参数
 * @returns Promise<any>
 */
export async function accountExchangeGroupApi(
  params: AIAccountApi.AccountExchangeGroupParams,
): Promise<any> {
  return requestClient.post(
    '/mobile/externalAccount/accountExchangeGroup',
    params,
  );
}
