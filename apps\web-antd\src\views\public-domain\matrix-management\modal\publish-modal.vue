<script>
import { ref, watch } from 'vue';

import { RightOutlined, SearchOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Card,
  Cascader,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Steps,
  // regionList
} from 'ant-design-vue';

const regionList = [
  {
    value: 'zhejiang',
    label: '浙江',
    children: [
      {
        value: 'hangzhou',
        label: '杭州',
        children: [
          {
            value: 'xihu',
            label: '西湖区',
          },
        ],
      },
    ],
  },
  {
    value: 'jiangsu',
    label: '江苏',
    children: [
      {
        value: 'nanjing',
        label: '南京',
        children: [
          {
            value: 'zhonghuamen',
            label: '中华门',
          },
        ],
      },
    ],
  },
];

export default {
  components: {
    [Modal.name]: Modal,
    [Steps.name]: Steps,
    [Form.name]: Form,
    [Form.Item.name]: Form.Item,
    [Input.name]: Input,
    [Select.name]: Select,
    [Select.Option.name]: Select.Option,
    [Radio.name]: Radio,
    [Radio.Group.name]: Radio.Group,
    [DatePicker.name]: DatePicker,
    [Card.name]: Card,
    [Card.Meta.name]: Card.Meta,
    [Button.name]: Button,
    [InputNumber.name]: InputNumber,
    [Row.name]: Row,
    [Col.name]: Col,
    [Cascader.name]: Cascader,
    RightOutlined,
    SearchOutlined,
  },
  props: {
    type: {
      type: Number,
      default: 1,
    },
  },
  setup(props) {
    const visible = ref(false);
    const title = ref('发布任务');
    const step = ref(1);
    const type = ref(props.type);
    watch(
      () => props.type,
      (newVal) => {
        type.value = newVal;
      },
    );
    const obj = ref({
      name: '',
      projectName: '',
      numberCount: 0,
      title: '', // 用于存储选择的标题
      content: '', // 用于存储选择的文案
      productId: '', // 用于存储商品ID
      linkType: 1, // 挂载类型，1-商家POI, 2-自定义链接, 3-不挂载,
      poiId: '',
      poiName: '',
      publishTimeType: 1,
      startDate: '',
      dayCount: 0,
      publishDays: 0,
      accountGroup: '',
      accountGroupName: '',
    });
    const keyword = ref('');
    const city = ref('');
    const cityName = ref('');
    const poiList = ref([]);
    const accountGroupList = ref([]);
    const groupArr = ref([]);
    const groupArrIndex = ref(-1);
    const futureDateIndex = ref(0);

    const selVideo = () => {
      // 选择素材逻辑
    };

    const getTitle = (type) => {
      console.warn('选择标题/文案', type);
      // type: 1-文案, 2-标题
      // 这里实现导航到标题/文案选择页面的逻辑
      // 选择完成后需要更新对应的字段
      // obj.value.title 或 obj.value.content
    };

    const selectedCityText = ref('请选择城市');

    const handleRegion = (value, selectedOptions) => {
      cityName.value = selectedOptions[selectedOptions.length - 1].label;
      city.value = selectedOptions[selectedOptions.length - 1].value;
      selectedCityText.value = selectedOptions.map((o) => o.label).join(' / ');
    };

    const getPoiList = () => {
      // 获取POI列表逻辑
    };

    const handlePoiChange = (value, option) => {
      obj.value.poiId = value;
      obj.value.poiName = option.children;
    };

    const selMountType = (type) => {
      obj.value.linkType = type;
    };

    const getStep = (newStep) => {
      // 验证逻辑
      step.value = newStep;
    };

    const handleAccountGroupChange = (value, option) => {
      obj.value.accountGroup = value;
      obj.value.accountGroupName = option.children;
    };

    const getSelAccount = () => {
      // 选择账户逻辑
    };

    const delGroup = (index) => {
      groupArr.value.splice(index, 1);
    };

    const getBatch = (index) => {
      groupArrIndex.value = index;
      // 打开时间选择弹窗
    };

    const releaseBut = () => {
      // 发布逻辑
    };

    const handleOk = () => {
      // 确认逻辑
    };

    const handleCancel = () => {
      visible.value = false;
    };

    return {
      visible,
      title,
      step,
      // type,
      obj,
      keyword,
      city,
      cityName,
      poiList,
      accountGroupList,
      groupArr,
      groupArrIndex,
      futureDateIndex,
      regionList,
      selVideo,
      getTitle,
      handleRegion,
      getPoiList,
      handlePoiChange,
      selMountType,
      getStep,
      handleAccountGroupChange,
      getSelAccount,
      delGroup,
      getBatch,
      releaseBut,
      handleOk,
      handleCancel,
    };
  },
};
</script>

<template>
  <a-modal
    :visible="visible"
    :title="title"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
  >
    <a-steps :current="step - 1">
      <a-step title="基本信息" />
      <a-step title="发布设置" />
    </a-steps>

    <div v-if="step === 1" class="step-content">
      <a-form layout="vertical">
        <!-- 任务名称 -->
        <a-form-item label="任务名称" required>
          <a-input
            v-model="obj.name"
            placeholder="请输入任务名称"
            class="custom-input"
          />
        </a-form-item>

        <!-- 选择素材 -->
        <a-form-item label="选择素材" required>
          <a-input
            v-model="obj.projectName"
            placeholder="请选择素材文件夹"
            @click="selVideo"
            class="custom-input"
          >
            <template #suffix>
              <RightOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 视频数量 -->
        <a-form-item v-if="obj.projectName" label="视频数量" required>
          <a-input-number
            v-model="obj.numberCount"
            :min="0"
            disabled
            class="custom-input"
          />
        </a-form-item>

        <!-- 选择标题 -->
        <a-form-item label="选择标题" required>
          <a-input
            v-model="obj.title"
            placeholder="请选择标题"
            @click="getTitle(2)"
            class="custom-input"
          >
            <template #suffix>
              <RightOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 选择文案 -->
        <a-form-item label="选择文案" required>
          <a-input
            v-model="obj.content"
            placeholder="请选择文案"
            @click="getTitle(1)"
            class="custom-input"
          >
            <template #suffix>
              <RightOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 商品ID -->
        <a-form-item v-if="type === 4" label="商品ID">
          <a-input
            v-model="obj.productId"
            placeholder="请输入商品ID"
            class="custom-input"
          />
        </a-form-item>

        <div v-if="type === 1 || type === 2">
          <!-- 挂载类型 -->
          <a-form-item label="挂载类型">
            <a-radio-group
              v-model:value="obj.linkType"
              class="mount-type-group"
            >
              <a-radio :value="1" class="mount-type-radio">商家POI</a-radio>
              <a-radio :value="2" class="mount-type-radio">自定义链接</a-radio>
              <a-radio :value="3" class="mount-type-radio">不挂载</a-radio>
            </a-radio-group>

            <!-- 商家POI相关字段 -->
            <template v-if="obj.linkType === 1">
              <a-form-item label="选择城市" required>
                <a-cascader
                  v-model="city"
                  :options="regionList"
                  placeholder="请选择城市"
                  @change="handleRegion"
                  class="custom-input"
                />
              </a-form-item>

              <a-form-item label="商家简称" required>
                <a-input
                  v-model="keyword"
                  placeholder="请输入商家简称搜索POI地址"
                  @press-enter="getPoiList"
                  class="custom-input"
                >
                  <template #suffix>
                    <SearchOutlined @click="getPoiList" />
                  </template>
                </a-input>
              </a-form-item>

              <a-form-item label="POI地址" required>
                <a-select
                  v-model="obj.poiId"
                  placeholder="请选择POI地址"
                  @change="handlePoiChange"
                  class="custom-input"
                >
                  <a-select-option
                    v-for="item in poiList"
                    :key="item.poi_id"
                    :value="item.poi_id"
                  >
                    {{ item.poi_name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </template>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <div v-if="step === 2" class="step-content">
      <a-form layout="vertical">
        <a-form-item label="发布时间" required>
          <a-radio-group v-model:value="obj.publishTimeType">
            <a-radio :value="1">随机</a-radio>
            <a-radio :value="2">指定</a-radio>
          </a-radio-group>

          <template v-if="obj.publishTimeType === 1">
            <a-form-item label="开始发布日期" required>
              <a-date-picker
                v-model="obj.startDate"
                placeholder="请选择开始发布日期"
              />
            </a-form-item>

            <a-form-item label="每天发布数量" required>
              <a-input-number
                v-model="obj.dayCount"
                :min="1"
                :max="35"
                placeholder="请输入每天发布数量(最大值35)"
              />
            </a-form-item>

            <a-form-item label="发布天数" required>
              <a-input-number
                v-model="obj.publishDays"
                :min="1"
                placeholder="请输入发布天数"
              />
            </a-form-item>
          </template>
        </a-form-item>

        <a-form-item label="账户分组" required>
          <a-select
            v-model="obj.accountGroup"
            placeholder="请选择账户分组"
            @change="handleAccountGroupChange"
          >
            <a-select-option
              v-for="item in accountGroupList"
              :key="item.id"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="选择账户" required>
          <a-button @click="getSelAccount">请选择账户</a-button>
        </a-form-item>

        <a-row :gutter="16" v-if="groupArr.length > 0">
          <a-col
            v-for="(item, index) in groupArr"
            :key="index"
            :span="8"
            style="margin-bottom: 16px"
          >
            <a-card>
              <template #actions>
                <a-button type="link" @click="delGroup(index)">删除</a-button>
                <a-button
                  v-if="obj.publishTimeType === 2"
                  type="link"
                  @click="getBatch(index)"
                >
                  {{ item.publishTime || '请指定时间' }}
                </a-button>
              </template>
              <a-card-meta :title="item.account_name" />
            </a-card>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <template #footer>
      <a-button v-if="step === 2" @click="getStep(1)">上一步</a-button>
      <a-button v-else @click="handleCancel">取消</a-button>
      <a-button v-if="step === 1" type="primary" @click="getStep(2)">
        下一步
      </a-button>
      <a-button v-else type="primary" @click="releaseBut"> 完成发布 </a-button>
    </template>
  </a-modal>
</template>

<style scoped>
.step-content {
  padding: 0 16px;
  margin-top: 24px;
}
</style>
