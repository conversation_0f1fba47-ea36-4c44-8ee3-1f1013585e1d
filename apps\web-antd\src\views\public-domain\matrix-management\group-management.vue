<script setup lang="ts">
import type {
  AccountExchangeGroupParams,
  AccountGroupItem,
  AddAccountGroupParams,
  DelAccountGroupParams,
  DelAccountParams,
  GroupAccountItem,
  GroupAccountListParams,
  GroupManagementListParams,
} from '#/api/core';

import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { Page } from '@vben/common-ui';

import {
  Avatar as AAvatar,
  Badge as ABadge,
  <PERSON><PERSON> as AButton,
  Card as ACard,
  Col as ACol,
  Drawer as ADrawer,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Modal as AModal,
  Pagination as APagination,
  Row as ARow,
  Select as ASelect,
  SelectOption as ASelectOption,
  Spin as ASpin,
  Statistic as AStatistic,
  TabPane as ATabPane,
  Tabs as ATabs,
  Tag as ATag,
  message,
} from 'ant-design-vue';

import {
  accountExchangeGroup,
  addAccountGroup,
  delAccount,
  delAccountGroup,
  getGroupAccountList,
  getGroupManagementList,
} from '#/api/core';

// 初始化路由
const router = useRouter();

// 平台选项配置
const platformOptions = [
  { key: 1, label: '抖音', icon: '🎵' },
  { key: 2, label: '快手', icon: '⚡' },
  { key: 3, label: '视频号', icon: '📹' },
  { key: 4, label: '小红书', icon: '📖' },
];

// 响应式数据
const activeTab = ref<number>(1); // 当前选中的平台
const loading = ref(false); // 加载状态
const groupList = ref<AccountGroupItem[]>([]); // 分组列表数据

// 添加分组弹窗相关状态
const addModalVisible = ref(false); // 添加分组弹窗显示状态
const addFormLoading = ref(false); // 添加表单提交加载状态
const addFormData = ref({
  name: '', // 分组名称
  type: 1, // 平台类型，默认抖音
});

// 编辑分组弹窗相关状态
const editDrawerVisible = ref(false); // 编辑分组抽屉显示状态
const editLoading = ref(false); // 编辑页面加载状态
const currentEditGroup = ref<AccountGroupItem | null>(null); // 当前编辑的分组
const accountList = ref<GroupAccountItem[]>([]); // 分组内账号列表

// 账号列表分页配置
const accountPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: false,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `共 ${total} 个账号，第 ${range[0]}-${range[1]} 个`,
});

// 换组弹窗相关状态
const changeGroupModalVisible = ref(false); // 换组弹窗显示状态
const changeGroupLoading = ref(false); // 换组操作加载状态
const currentChangeAccount = ref<GroupAccountItem | null>(null); // 当前要换组的账号
const availableGroups = ref<AccountGroupItem[]>([]); // 可选分组列表
const selectedGroupId = ref<number | undefined>(undefined); // 选中的分组ID

// 分页配置
const pagination = ref({
  current: 1, // 当前页码
  pageSize: 12, // 每页数量
  total: 0, // 总数量
  showSizeChanger: false, // 不显示页面大小选择器
  showQuickJumper: true, // 显示快速跳转
  showTotal: (total: number, range: [number, number]) =>
    `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
});

// 计算当前平台名称
const currentPlatformName = computed(() => {
  const platform = platformOptions.find((p) => p.key === activeTab.value);
  return platform?.label || '';
});

// 工具函数：检查账号授权状态
const checkAccountAuth = (expiresIn: string): boolean => {
  try {
    const now = new Date();
    const expireDate = new Date(expiresIn.replaceAll('-', '/'));
    return now < expireDate;
  } catch {
    return false;
  }
};

// 工具函数：格式化日期
const formatDate = (dateStr: string): string => {
  try {
    return new Date(dateStr).toLocaleString('zh-CN');
  } catch {
    return dateStr;
  }
};

// 加载分组列表数据
const loadGroupList = async () => {
  try {
    loading.value = true;

    const params: GroupManagementListParams = {
      page: pagination.value.current,
      psize: pagination.value.pageSize,
      type: activeTab.value,
    };

    const response = await getGroupManagementList(params);

    groupList.value = response.list;
    pagination.value.total = response.total;
    pagination.value.current = response.pindex;
  } catch (error) {
    console.error('加载分组列表失败:', error);
    message.error('加载分组列表失败，请稍后重试');
    groupList.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
};

// 加载分组账号列表数据
const loadGroupAccountList = async () => {
  if (!currentEditGroup.value) return;

  try {
    editLoading.value = true;

    const params: GroupAccountListParams = {
      account_group_id: currentEditGroup.value.id,
      limit: accountPagination.value.pageSize,
      page: accountPagination.value.current,
      type: activeTab.value,
    };

    const response = await getGroupAccountList(params);

    accountList.value = response.list;
    accountPagination.value.total = response.total;
    accountPagination.value.current = response.pindex;
  } catch (error) {
    console.error('加载账号列表失败:', error);
    message.error('加载账号列表失败，请稍后重试');
    accountList.value = [];
    accountPagination.value.total = 0;
  } finally {
    editLoading.value = false;
  }
};

// 加载可选分组列表
const loadAvailableGroups = async () => {
  try {
    const params: GroupManagementListParams = {
      page: 1,
      psize: 2000, // 设置为2000获取所有分组
      type: activeTab.value,
    };

    const response = await getGroupManagementList(params);
    availableGroups.value = response.list;
  } catch (error) {
    console.error('加载分组列表失败:', error);
    message.error('加载分组列表失败，请稍后重试');
    availableGroups.value = [];
  }
};

// 处理平台切换
const handleTabChange = (key: number | string) => {
  activeTab.value = typeof key === 'string' ? Number(key) : key;
  pagination.value.current = 1; // 重置到第一页
};

// 处理分页变化
const handlePageChange = (page: number, pageSize: number) => {
  pagination.value.current = page;
  pagination.value.pageSize = pageSize;
  loadGroupList();
};

// 打开添加分组弹窗
const handleAddGroup = () => {
  addFormData.value = {
    name: '',
    type: activeTab.value, // 默认选择当前平台
  };
  addModalVisible.value = true;
};

// 关闭添加分组弹窗
const handleAddModalCancel = () => {
  addModalVisible.value = false;
  addFormData.value = {
    name: '',
    type: 1,
  };
};

// 提交添加分组表单
const handleAddSubmit = async () => {
  // 表单验证
  const name = addFormData.value.name.trim();
  if (!name) {
    message.error('请输入分组名称');
    return;
  }
  if (name.length === 0 || name.length > 20) {
    message.error('分组名称长度为1-20个字符');
    return;
  }
  if (!addFormData.value.type) {
    message.error('请选择平台');
    return;
  }

  try {
    addFormLoading.value = true;

    const params: AddAccountGroupParams = {
      name,
      type: addFormData.value.type,
    };

    await addAccountGroup(params);
    message.success('添加分组成功');

    // 关闭弹窗并重置表单
    handleAddModalCancel();

    // 如果添加的分组是当前平台，则刷新列表
    if (addFormData.value.type === activeTab.value) {
      await loadGroupList();
    }
  } catch (error) {
    console.error('添加分组失败:', error);
    message.error('添加分组失败，请稍后重试');
  } finally {
    addFormLoading.value = false;
  }
};

// 处理编辑操作
const handleEdit = (group: AccountGroupItem) => {
  currentEditGroup.value = group;
  accountPagination.value.current = 1; // 重置到第一页
  editDrawerVisible.value = true;
  loadGroupAccountList();
};

// 关闭编辑抽屉
const handleEditDrawerClose = () => {
  editDrawerVisible.value = false;
  currentEditGroup.value = null;
  accountList.value = [];
  accountPagination.value.current = 1;
  accountPagination.value.total = 0;
};

// 处理账号列表分页变化
const handleAccountPageChange = (page: number, pageSize: number) => {
  accountPagination.value.current = page;
  accountPagination.value.pageSize = pageSize;
  loadGroupAccountList();
};

// 处理换组操作
const handleChangeGroup = async (account: GroupAccountItem) => {
  currentChangeAccount.value = account;
  selectedGroupId.value = undefined;

  // 加载可选分组列表
  await loadAvailableGroups();

  changeGroupModalVisible.value = true;
};

// 关闭换组弹窗
const handleChangeGroupModalCancel = () => {
  changeGroupModalVisible.value = false;
  currentChangeAccount.value = null;
  selectedGroupId.value = undefined;
  availableGroups.value = [];
};

// 确认换组操作
const handleChangeGroupSubmit = async () => {
  if (!currentChangeAccount.value || !selectedGroupId.value) {
    message.error('请选择目标分组');
    return;
  }

  try {
    changeGroupLoading.value = true;

    const params: AccountExchangeGroupParams = {
      account_group_id: selectedGroupId.value,
      id: [currentChangeAccount.value.id], // 将账户ID放在数组中
    };

    await accountExchangeGroup(params);
    message.success('换组成功');

    // 关闭弹窗
    handleChangeGroupModalCancel();

    // 刷新账号列表
    await loadGroupAccountList();
  } catch (error) {
    console.error('换组失败:', error);
    message.error('换组失败，请稍后重试');
  } finally {
    changeGroupLoading.value = false;
  }
};

// 处理删除账号操作
const handleDeleteAccount = (account: GroupAccountItem) => {
  AModal.confirm({
    title: '确认删除',
    content: '确认删除该账户?',
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const params: DelAccountParams = {
          id: [account.id],
        };

        await delAccount(params);
        message.success('删除账户成功');

        // 刷新账号列表
        await loadGroupAccountList();
      } catch (error) {
        console.error('删除账户失败:', error);
        message.error('删除账户失败，请稍后重试');
      }
    },
  });
};

// 处理删除操作
const handleDelete = (group: AccountGroupItem) => {
  // 检查分组内是否有账户
  if (group.account_count > 0) {
    message.error('分组内有账户数据,删除失败!');
    return;
  }

  // 二次确认删除
  AModal.confirm({
    title: '确认删除',
    content: `确认删除该分组"${group.name}"?`,
    okText: '确认删除',
    okType: 'danger',
    cancelText: '取消',
    onOk: async () => {
      try {
        const params: DelAccountGroupParams = {
          id: group.id,
        };

        await delAccountGroup(params);
        message.success('删除分组成功');

        // 刷新列表
        await loadGroupList();
      } catch (error) {
        console.error('删除分组失败:', error);
        message.error('删除分组失败，请稍后重试');
      }
    },
  });
};

// 监听平台切换，重新加载数据
watch(activeTab, () => {
  loadGroupList();
});

// 返回到矩阵管理首页
const handleGoBack = () => {
  router.push('/public-domain/matrix-management-dashboard');
};

// 页面挂载时加载数据
onMounted(() => {
  loadGroupList();
});
</script>

<template>
  <Page auto-content-height>
    <div class="group-management-container">
      <!-- 返回按钮 -->
      <div class="page-header">
        <AButton @click="handleGoBack" class="back-btn"> ← 返回 </AButton>
      </div>

      <!-- 平台选择标签页和添加按钮 -->
      <div class="tabs-header">
        <ATabs
          v-model:active-key="activeTab"
          @change="handleTabChange"
          class="platform-tabs"
        >
          <ATabPane
            v-for="platform in platformOptions"
            :key="platform.key"
            :tab="`${platform.icon} ${platform.label}`"
          />
        </ATabs>

        <div class="header-actions">
          <AButton type="primary" @click="handleAddGroup" class="add-group-btn">
            ➕ 添加分组
          </AButton>
        </div>
      </div>

      <!-- 分组列表内容区域 -->
      <div class="group-content">
        <ASpin :spinning="loading" tip="加载中...">
          <!-- 分组卡片列表 -->
          <div v-if="groupList.length > 0" class="group-cards-container">
            <ARow :gutter="[16, 16]">
              <ACol
                v-for="group in groupList"
                :key="group.id"
                :xs="24"
                :sm="12"
                :md="8"
                :lg="6"
                :xl="6"
              >
                <ACard class="group-card" hoverable>
                  <!-- 分组名称 -->
                  <div class="group-name">{{ group.name }}</div>

                  <!-- 统计数据 -->
                  <div class="group-stats">
                    <div class="stat-item">
                      <span class="stat-label">账号数量</span>
                      <span class="stat-value">{{ group.account_count }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">曝光量</span>
                      <span class="stat-value">{{ group.exposure_count }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">点赞数</span>
                      <span class="stat-value">{{ group.like_count }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">评论数</span>
                      <span class="stat-value">{{ group.comment }}</span>
                    </div>
                  </div>

                  <!-- 操作按钮 -->
                  <div class="group-actions">
                    <AButton
                      type="primary"
                      size="small"
                      @click="handleEdit(group)"
                    >
                      编辑
                    </AButton>
                    <AButton
                      type="primary"
                      danger
                      size="small"
                      @click="handleDelete(group)"
                    >
                      删除
                    </AButton>
                  </div>
                </ACard>
              </ACol>
            </ARow>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!loading" class="empty-state">
            <div class="empty-icon">📁</div>
            <div class="empty-text">暂无{{ currentPlatformName }}分组数据</div>
            <div class="empty-description">请先创建分组或切换其他平台查看</div>
          </div>
        </ASpin>

        <!-- 分页组件 -->
        <div v-if="pagination.total > 0" class="pagination-wrapper">
          <APagination
            v-model:current="pagination.current"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :show-size-changer="pagination.showSizeChanger"
            :show-quick-jumper="pagination.showQuickJumper"
            :show-total="pagination.showTotal"
            @change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 添加分组弹窗 -->
    <AModal
      v-model:visible="addModalVisible"
      title="添加分组"
      width="500px"
      :confirm-loading="addFormLoading"
      @ok="handleAddSubmit"
      @cancel="handleAddModalCancel"
    >
      <AForm :model="addFormData" layout="vertical" class="add-group-form">
        <AFormItem label="平台选择" name="type" required>
          <ARadioGroup v-model:value="addFormData.type">
            <ARadio
              v-for="platform in platformOptions"
              :key="platform.key"
              :value="platform.key"
            >
              {{ platform.icon }} {{ platform.label }}
            </ARadio>
          </ARadioGroup>
        </AFormItem>

        <AFormItem label="分组名称" name="name" required>
          <AInput
            v-model:value="addFormData.name"
            placeholder="请输入分组名称"
            :maxlength="20"
            show-count
          />
        </AFormItem>
      </AForm>
    </AModal>

    <!-- 编辑分组抽屉 -->
    <ADrawer
      v-model:visible="editDrawerVisible"
      :title="`分组详情 - ${currentEditGroup?.name || ''}`"
      width="800px"
      placement="right"
      @close="handleEditDrawerClose"
    >
      <div v-if="currentEditGroup" class="edit-drawer-content">
        <!-- 分组统计信息 -->
        <div class="group-stats-header">
          <ARow :gutter="16">
            <ACol :span="8">
              <AStatistic
                title="曝光数"
                :value="currentEditGroup.exposure_count"
                :value-style="{ color: '#1890ff' }"
              />
            </ACol>
            <ACol :span="8">
              <AStatistic
                title="点赞数"
                :value="currentEditGroup.like_count"
                :value-style="{ color: '#52c41a' }"
              />
            </ACol>
            <ACol :span="8">
              <AStatistic
                title="评论数"
                :value="currentEditGroup.comment"
                :value-style="{ color: '#fa8c16' }"
              />
            </ACol>
          </ARow>
        </div>

        <!-- 账号列表 -->
        <div class="account-list-section">
          <h3 class="section-title">账号列表</h3>

          <ASpin :spinning="editLoading" tip="加载中...">
            <div v-if="accountList.length > 0">
              <!-- 账号网格布局 -->
              <div class="account-grid">
                <ARow :gutter="[16, 16]">
                  <ACol
                    v-for="item in accountList"
                    :key="item.id"
                    :xs="24"
                    :sm="12"
                    :md="8"
                    :lg="8"
                    :xl="8"
                  >
                    <ACard class="account-card" hoverable>
                      <!-- 授权状态标识 - 右上角 -->
                      <div class="auth-status-badge">
                        <ABadge
                          :status="
                            checkAccountAuth(item.expires_in)
                              ? 'success'
                              : 'error'
                          "
                          :text="
                            checkAccountAuth(item.expires_in)
                              ? '已授权'
                              : '已失效'
                          "
                        />
                      </div>

                      <!-- 账号头像 -->
                      <div class="account-avatar-section">
                        <AAvatar
                          :src="item.avatar"
                          :size="64"
                          class="account-avatar"
                        />
                      </div>

                      <!-- 账号信息 -->
                      <div class="account-info-section">
                        <div class="account-name">{{ item.account_name }}</div>
                        <ATag color="blue" class="group-tag">
                          {{ item.account_group_name }}
                        </ATag>
                      </div>

                      <!-- 统计数据 -->
                      <div class="account-stats-section">
                        <div class="stats-row">
                          <div class="stat-item">
                            <span class="stat-label">曝光</span>
                            <span class="stat-value">{{
                              item.exposure_count
                            }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">点赞</span>
                            <span class="stat-value">{{
                              item.like_count
                            }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">评论</span>
                            <span class="stat-value">{{ item.comment }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 到期时间 -->
                      <div class="account-expire-section">
                        <span class="expire-label">到期时间:</span>
                        <span class="expire-time">{{
                          formatDate(item.expires_in)
                        }}</span>
                      </div>

                      <!-- 操作按钮 -->
                      <div class="account-actions">
                        <AButton
                          type="primary"
                          size="small"
                          @click="handleChangeGroup(item)"
                        >
                          换组
                        </AButton>
                        <AButton
                          type="primary"
                          danger
                          size="small"
                          @click="handleDeleteAccount(item)"
                        >
                          删除
                        </AButton>
                      </div>
                    </ACard>
                  </ACol>
                </ARow>
              </div>

              <!-- 账号列表分页 -->
              <div
                v-if="accountPagination.total > 0"
                class="account-pagination"
              >
                <APagination
                  v-model:current="accountPagination.current"
                  v-model:page-size="accountPagination.pageSize"
                  :total="accountPagination.total"
                  :show-size-changer="accountPagination.showSizeChanger"
                  :show-quick-jumper="accountPagination.showQuickJumper"
                  :show-total="accountPagination.showTotal"
                  @change="handleAccountPageChange"
                />
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else-if="!editLoading" class="empty-account-state">
              <div class="empty-icon">👤</div>
              <div class="empty-text">该分组暂无账号</div>
              <div class="empty-description">请添加账号到此分组</div>
            </div>
          </ASpin>
        </div>
      </div>
    </ADrawer>

    <!-- 换组弹窗 -->
    <AModal
      v-model:visible="changeGroupModalVisible"
      :title="`账号换组 - ${currentChangeAccount?.account_name || ''}`"
      width="500px"
      :confirm-loading="changeGroupLoading"
      @ok="handleChangeGroupSubmit"
      @cancel="handleChangeGroupModalCancel"
    >
      <div v-if="currentChangeAccount" class="change-group-content">
        <div class="current-account-info">
          <AAvatar :src="currentChangeAccount.avatar" :size="40" />
          <div class="account-details">
            <div class="account-name">
              {{ currentChangeAccount.account_name }}
            </div>
            <div class="current-group">
              当前分组: {{ currentChangeAccount.account_group_name }}
            </div>
          </div>
        </div>

        <div class="group-selection">
          <h4>选择目标分组:</h4>
          <ASelect
            v-model:value="selectedGroupId"
            placeholder="请选择目标分组"
            class="group-select"
            :disabled="availableGroups.length === 0"
          >
            <ASelectOption
              v-for="group in availableGroups"
              :key="group.id"
              :value="group.id"
              :disabled="group.id === currentChangeAccount.account_group_id"
            >
              <div class="select-option-content">
                <span class="group-name">{{ group.name }}</span>
                <span class="group-info">
                  ({{ group.account_count }}个账号)
                  <span
                    v-if="group.id === currentChangeAccount.account_group_id"
                    class="current-tag"
                  >
                    - 当前分组
                  </span>
                </span>
              </div>
            </ASelectOption>
          </ASelect>
        </div>
      </div>
    </AModal>
  </Page>
</template>

<style scoped lang="scss">
// 响应式设计
@media (max-width: 768px) {
  .group-management-container {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .group-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .stat-item {
    padding: 8px;
  }

  .group-actions {
    flex-direction: column;

    .ant-btn {
      flex: none;
    }
  }

  // 账号卡片在移动端的调整
  .account-card {
    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .account-stats-section {
    .stats-row {
      .stat-item {
        .stat-value {
          font-size: 14px;
        }
      }
    }
  }

  .account-actions {
    flex-direction: column;
    gap: 6px;

    .ant-btn {
      flex: none;
    }
  }
}

.group-management-container {
  min-height: 100vh;
  padding: 24px;
  background: var(--ant-color-bg-layout);
}

// 页面头部
.page-header {
  margin-bottom: 16px;
}

// 标签页头部区域
.tabs-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

// 平台标签页样式 - 与矩阵管理页面保持一致
.platform-tabs {
  flex: 1;

  :deep(.ant-tabs-tab) {
    font-size: 16px;
    font-weight: 500;

    &.ant-tabs-tab-active {
      font-weight: 600;
    }
  }

  :deep(.ant-tabs-content-holder) {
    display: none;
  }
}

// 头部操作按钮区域
.header-actions {
  display: flex;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: center;
  }
}

.back-btn,
.add-group-btn {
  height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    transform: translateY(-1px);
  }
}

// 分组内容区域
.group-content {
  min-height: 400px;
}

// 分组卡片容器
.group-cards-container {
  margin-bottom: 32px;
}

// 分组卡片样式 - 与矩阵管理页面保持一致
.group-card {
  height: 100%;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
    transform: translateY(-4px);
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 24px;
  }
}

// 分组名称
.group-name {
  margin-bottom: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.3;
  color: var(--ant-color-text);
  text-align: center;
  white-space: nowrap;
}

// 统计数据样式
.group-stats {
  display: grid;
  flex: 1;
  grid-template-columns: 1fr 1fr;
  gap: 14px;
  margin-bottom: 22px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 14px 10px;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--ant-color-fill-tertiary);
    border-color: var(--ant-color-primary-border);
  }
}

.stat-label {
  margin-bottom: 6px;
  font-size: 13px;
  font-weight: 500;
  color: var(--ant-color-text-secondary);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--ant-color-text);
}

// 操作按钮样式
.group-actions {
  display: flex;
  gap: 8px;
  justify-content: center;

  .ant-btn {
    flex: 1;
    font-weight: 500;
    border-radius: 6px;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  font-size: 64px;
  opacity: 0.6;
}

.empty-text {
  margin-bottom: 8px;
  font-size: 18px;
  font-weight: 500;
  color: var(--ant-color-text);
}

.empty-description {
  font-size: 14px;
  color: var(--ant-color-text-secondary);
}

// 分页样式
.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding: 24px 0;
  border-top: 1px solid var(--ant-color-border);
}

// 暗黑主题适配
:deep(.ant-spin-nested-loading) {
  .ant-spin-container {
    background: transparent;
  }
}

:deep(.ant-tabs-card) {
  .ant-tabs-tab {
    background: var(--ant-color-bg-container);
    border-color: var(--ant-color-border);

    &.ant-tabs-tab-active {
      color: white;
      background: var(--ant-color-primary);
      border-color: var(--ant-color-primary);
    }
  }
}

// 添加分组弹窗样式
.add-group-form {
  padding-top: 16px;

  .ant-form-item {
    margin-bottom: 20px;
  }

  .ant-radio-group {
    width: 100%;

    .ant-radio-wrapper {
      display: block;
      padding: 12px 16px;
      margin-bottom: 12px;
      background: var(--ant-color-bg-container);
      border: 1px solid var(--ant-color-border);
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--ant-color-fill-tertiary);
        border-color: var(--ant-color-primary-border);
      }

      &.ant-radio-wrapper-checked {
        background: var(--ant-color-primary-bg);
        border-color: var(--ant-color-primary);
      }
    }
  }

  .ant-input {
    height: 40px;
    border-radius: 6px;
  }
}

// 弹窗暗黑主题适配
:deep(.ant-modal-content) {
  background: var(--ant-color-bg-container);
}

:deep(.ant-modal-header) {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
}

:deep(.ant-modal-title) {
  color: var(--ant-color-text);
}

:deep(.ant-form-item-label > label) {
  color: var(--ant-color-text);
}

// 编辑抽屉样式
.edit-drawer-content {
  padding: 0;
}

.group-stats-header {
  padding: 24px;
  margin-bottom: 24px;
  background: var(--ant-color-bg-container);
  border: 1px solid var(--ant-color-border);
  border-radius: 8px;
}

.section-title {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: var(--ant-color-text);
}

.account-list-section {
  // 账号网格布局
  .account-grid {
    margin-bottom: 24px;
  }
}

// 账号卡片样式
.account-card {
  position: relative;
  height: 100%;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgb(0 0 0 / 15%);
    transform: translateY(-2px);
  }

  :deep(.ant-card-body) {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 20px;
  }
}

// 授权状态标识 - 右上角
.auth-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1;

  :deep(.ant-badge-status-text) {
    font-size: 11px;
    font-weight: 500;
  }
}

// 账号头像区域
.account-avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;

  .account-avatar {
    border: 2px solid var(--ant-color-border);
  }
}

// 账号信息区域
.account-info-section {
  margin-bottom: 16px;
  text-align: center;

  .account-name {
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 16px;
    font-weight: 600;
    color: var(--ant-color-text);
    white-space: nowrap;
  }

  .group-tag {
    font-size: 12px;
  }
}

// 统计数据区域
.account-stats-section {
  margin-bottom: 16px;

  .stats-row {
    display: flex;
    justify-content: space-around;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-label {
        margin-bottom: 4px;
        font-size: 12px;
        color: var(--ant-color-text-secondary);
      }

      .stat-value {
        font-size: 16px;
        font-weight: 700;
        color: var(--ant-color-primary);
      }
    }
  }
}

// 到期时间区域
.account-expire-section {
  padding: 8px 12px;
  margin-bottom: 16px;
  text-align: center;
  background: var(--ant-color-fill-tertiary);
  border-radius: 6px;

  .expire-label {
    margin-right: 4px;
    font-size: 12px;
    color: var(--ant-color-text-secondary);
  }

  .expire-time {
    font-size: 12px;
    font-weight: 500;
    color: var(--ant-color-text);
  }
}

// 操作按钮区域
.account-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: auto;

  .ant-btn {
    flex: 1;
    font-weight: 500;
    border-radius: 6px;
  }
}

// 换组弹窗样式
.change-group-content {
  .current-account-info {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px;
    margin-bottom: 24px;
    background: var(--ant-color-bg-container);
    border: 1px solid var(--ant-color-border);
    border-radius: 8px;

    .account-details {
      flex: 1;

      .account-name {
        margin-bottom: 4px;
        font-size: 16px;
        font-weight: 600;
        color: var(--ant-color-text);
      }

      .current-group {
        font-size: 14px;
        color: var(--ant-color-text-secondary);
      }
    }
  }

  .group-selection {
    h4 {
      margin: 0 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: var(--ant-color-text);
    }

    .group-select {
      width: 100%;

      .ant-select-selector {
        height: 40px;
        border-radius: 6px;
      }
    }
  }
}

// 下拉选择器选项样式
:deep(.ant-select-dropdown) {
  .select-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .group-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--ant-color-text);
    }

    .group-info {
      font-size: 12px;
      color: var(--ant-color-text-secondary);

      .current-tag {
        font-weight: 500;
        color: var(--ant-color-warning);
      }
    }
  }

  .ant-select-item-option-disabled {
    .select-option-content {
      .group-name {
        color: var(--ant-color-text-quaternary);
      }

      .group-info {
        color: var(--ant-color-text-quaternary);

        .current-tag {
          color: var(--ant-color-warning);
          opacity: 0.6;
        }
      }
    }
  }
}

.account-pagination {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid var(--ant-color-border);
}

.empty-account-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .empty-icon {
    margin-bottom: 16px;
    font-size: 48px;
    opacity: 0.6;
  }

  .empty-text {
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    color: var(--ant-color-text);
  }

  .empty-description {
    font-size: 14px;
    color: var(--ant-color-text-secondary);
  }
}

// 抽屉暗黑主题适配
:deep(.ant-drawer-content) {
  background: var(--ant-color-bg-layout);
}

:deep(.ant-drawer-header) {
  background: var(--ant-color-bg-container);
  border-bottom: 1px solid var(--ant-color-border);
}

:deep(.ant-drawer-title) {
  color: var(--ant-color-text);
}
</style>
